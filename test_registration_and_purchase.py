#!/usr/bin/env python3
"""
Test script to verify registration team creation and marketplace purchase updates
"""

import sys
import psycopg2
import json
from datetime import datetime

# Database configuration
PG_CONFIG = {
    'host': 'localhost',
    'database': 'sapmmo',
    'user': 'alandoan',
    'password': '',
    'port': 5432
}

def test_registration_team_creation():
    """Test that registration creates teams properly"""
    print("🧪 Testing registration team creation...")
    
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        # Check recent users and their teams
        cursor.execute('''
            SELECT u.user_id, u.username, u.team_id, t.team_name, t.leader_id
            FROM "Users" u
            LEFT JOIN "Teams" t ON u.team_id = t.team_id
            WHERE u.is_deleted = 0
            ORDER BY u.user_id DESC
            LIMIT 10
        ''')
        
        users = cursor.fetchall()
        print(f"📋 Recent users and their teams:")
        
        users_without_teams = 0
        for user_id, username, team_id, team_name, leader_id in users:
            if team_id:
                leader_status = "✅ Leader" if leader_id == user_id else "👤 Member"
                print(f"   User: {username} (ID: {user_id}) -> Team: {team_name} (ID: {team_id}) {leader_status}")
            else:
                users_without_teams += 1
                print(f"   User: {username} (ID: {user_id}) -> ❌ No team")
        
        print(f"📊 Users without teams: {users_without_teams}")
        
        cursor.close()
        conn.close()
        
        return users_without_teams == 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_account_statistics():
    """Test account statistics calculation"""
    print("\n🧪 Testing account statistics...")
    
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        # Check accounts with sold_to_user_id but missing assigned_user_id or team_id
        cursor.execute('''
            SELECT COUNT(*) FROM "Accounts"
            WHERE sold_to_user_id IS NOT NULL 
            AND (assigned_user_id IS NULL OR team_id IS NULL)
            AND is_deleted = 0
        ''')
        
        missing_assignments = cursor.fetchone()[0]
        print(f"📊 Accounts with sold_to_user_id but missing assigned_user_id/team_id: {missing_assignments}")
        
        # Check accounts statistics by team
        cursor.execute('''
            SELECT t.team_name, 
                   COUNT(CASE WHEN a.assigned_user_id IS NOT NULL THEN 1 END) as assigned_accounts,
                   COUNT(CASE WHEN a.sold_to_user_id IS NOT NULL THEN 1 END) as sold_accounts,
                   COUNT(a.account_id) as total_accounts
            FROM "Teams" t
            LEFT JOIN "Accounts" a ON t.team_id = a.team_id AND a.is_deleted = 0
            WHERE t.team_id IS NOT NULL
            GROUP BY t.team_id, t.team_name
            HAVING COUNT(a.account_id) > 0
            ORDER BY total_accounts DESC
        ''')
        
        team_stats = cursor.fetchall()
        print(f"📋 Account statistics by team:")
        
        for team_name, assigned, sold, total in team_stats:
            print(f"   Team: {team_name} -> Total: {total}, Assigned: {assigned}, Sold: {sold}")
        
        # Check marketplace purchased accounts
        cursor.execute('''
            SELECT COUNT(*) FROM "Accounts"
            WHERE sold_to_user_id IS NOT NULL 
            AND assigned_user_id IS NOT NULL 
            AND team_id IS NOT NULL
            AND is_deleted = 0
        ''')
        
        properly_assigned = cursor.fetchone()[0]
        print(f"📊 Properly assigned marketplace accounts: {properly_assigned}")
        
        cursor.close()
        conn.close()
        
        return missing_assignments == 0
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def test_marketplace_purchase_simulation():
    """Simulate marketplace purchase to test assignment logic"""
    print("\n🧪 Testing marketplace purchase simulation...")
    
    try:
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        # Find a user without team for testing
        cursor.execute('''
            SELECT user_id, username, team_id FROM "Users"
            WHERE team_id IS NULL AND is_deleted = 0
            LIMIT 1
        ''')
        
        user_result = cursor.fetchone()
        if not user_result:
            print("✅ All users have teams - creating test scenario...")
            
            # Create a test user without team
            cursor.execute('''
                INSERT INTO "Users" (username, password, role, email, phone, unit_code, mp_balance, is_deleted)
                VALUES ('testuser_temp', 'temp', 'user', '<EMAIL>', '+84123456789', 'TEST001', 1000, 0)
                RETURNING user_id
            ''')
            
            test_user_id = cursor.fetchone()[0]
            test_username = 'testuser_temp'
            test_team_id = None
            print(f"   Created test user: {test_username} (ID: {test_user_id})")
        else:
            test_user_id, test_username, test_team_id = user_result
            print(f"   Found user without team: {test_username} (ID: {test_user_id})")
        
        # Simulate the team creation logic from assign_accounts_to_user
        if not test_team_id:
            team_name = test_username.capitalize()
            print(f"   Simulating team creation: {team_name}")
            
            cursor.execute('''
                INSERT INTO "Teams" (team_name, leader_id)
                VALUES (%s, %s)
                RETURNING team_id
            ''', (team_name, test_user_id))
            
            test_team_id = cursor.fetchone()[0]
            print(f"   ✅ Created team: {team_name} (ID: {test_team_id})")
            
            cursor.execute('''
                UPDATE "Users" 
                SET team_id = %s
                WHERE user_id = %s
            ''', (test_team_id, test_user_id))
            print(f"   ✅ Updated user with team_id: {test_team_id}")
        
        # Clean up test user if created
        if test_username == 'testuser_temp':
            cursor.execute('DELETE FROM "Users" WHERE user_id = %s', (test_user_id,))
            cursor.execute('DELETE FROM "Teams" WHERE team_id = %s', (test_team_id,))
            print(f"   🧹 Cleaned up test data")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print("✅ Marketplace purchase simulation completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🚀 Starting verification tests...\n")
    
    tests = [
        ("Registration Team Creation", test_registration_team_creation),
        ("Account Statistics", test_account_statistics),
        ("Marketplace Purchase Simulation", test_marketplace_purchase_simulation)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        print(f"{'='*50}")
        print(f"Running: {test_name}")
        print(f"{'='*50}")
        
        result = test_func()
        results.append((test_name, result))
        
        if result:
            print(f"✅ {test_name}: PASSED")
        else:
            print(f"❌ {test_name}: FAILED")
    
    print(f"\n{'='*50}")
    print("SUMMARY")
    print(f"{'='*50}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name}: {status}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The implementation is working correctly.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the implementation.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
