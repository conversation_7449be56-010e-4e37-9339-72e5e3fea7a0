#!/usr/bin/env python3
"""
Migration script for team features on Ubuntu server
- Creates teams for existing users who don't have teams
- Fixes sold accounts data (assigned_user_id and team_id)
- Verifies the migration results

Usage: python3 migrate_team_features.py
"""

import sys
import os
import psycopg2
from datetime import datetime, timezone, timedelta

# Import database configuration
try:
    from db_config import PG_CONFIG
except ImportError:
    print("❌ Error: Cannot import db_config.py")
    print("Make sure db_config.py exists in the same directory")
    sys.exit(1)

def get_vn_time():
    """Get current Vietnam time"""
    vn_tz = timezone(timedelta(hours=7))
    return datetime.now(vn_tz)

def test_database_connection():
    """Test database connection"""
    try:
        print("🔍 Testing database connection...")
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        cursor.execute('SELECT version()')
        version = cursor.fetchone()[0]
        print(f"✅ Connected to PostgreSQL: {version}")
        cursor.close()
        conn.close()
        return True
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

def create_teams_for_existing_users():
    """Create teams for users who don't have teams"""
    try:
        print("\n🔧 Step 1: Creating teams for existing users...")
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        # Find users without teams
        cursor.execute('''
            SELECT user_id, username FROM "Users"
            WHERE team_id IS NULL AND is_deleted = 0
            ORDER BY user_id
        ''')
        
        users_without_teams = cursor.fetchall()
        print(f"📊 Found {len(users_without_teams)} users without teams")
        
        if not users_without_teams:
            print("✅ All users already have teams!")
            cursor.close()
            conn.close()
            return True, 0, 0
        
        teams_created = 0
        users_updated = 0
        
        for user_id, username in users_without_teams:
            team_name = username.capitalize()
            print(f"🔧 Processing user {username}...")
            
            # Check if team name already exists
            cursor.execute('SELECT team_id FROM "Teams" WHERE team_name = %s', (team_name,))
            existing_team = cursor.fetchone()
            
            if existing_team:
                team_id = existing_team[0]
                print(f"   ✅ Using existing team: {team_name} (ID: {team_id})")
            else:
                # Create new team
                cursor.execute('''
                    INSERT INTO "Teams" (team_name, leader_id)
                    VALUES (%s, %s)
                    RETURNING team_id
                ''', (team_name, user_id))
                
                team_id = cursor.fetchone()[0]
                teams_created += 1
                print(f"   ✅ Created team: {team_name} (ID: {team_id})")
            
            # Update user with team_id
            cursor.execute('''
                UPDATE "Users" 
                SET team_id = %s
                WHERE user_id = %s
            ''', (team_id, user_id))
            
            users_updated += 1
            print(f"   ✅ Updated user {username} with team_id: {team_id}")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"📊 Teams created: {teams_created}, Users updated: {users_updated}")
        return True, teams_created, users_updated
        
    except Exception as e:
        print(f"❌ Team creation failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False, 0, 0

def fix_sold_accounts_data():
    """Fix sold accounts data - update assigned_user_id and team_id"""
    try:
        print("\n🔧 Step 2: Fixing sold accounts data...")
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        # Find accounts that need fixing
        cursor.execute('''
            SELECT account_id, account_name, sold_to_user_id, assigned_user_id, team_id
            FROM "Accounts"
            WHERE sold_to_user_id IS NOT NULL 
            AND (assigned_user_id IS NULL OR team_id IS NULL)
            AND is_deleted = 0
            ORDER BY account_id
        ''')
        
        accounts_to_fix = cursor.fetchall()
        print(f"📊 Found {len(accounts_to_fix)} accounts that need fixing")
        
        if not accounts_to_fix:
            print("✅ No accounts need fixing!")
            cursor.close()
            conn.close()
            return True, 0
        
        accounts_updated = 0
        
        for account_id, account_name, sold_to_user_id, assigned_user_id, team_id in accounts_to_fix:
            # Get user's current team_id
            cursor.execute('''
                SELECT username, team_id FROM "Users" 
                WHERE user_id = %s AND is_deleted = 0
            ''', (sold_to_user_id,))
            
            user_result = cursor.fetchone()
            if not user_result:
                print(f"⚠️  User {sold_to_user_id} not found for account {account_name}, skipping...")
                continue
            
            username, user_team_id = user_result
            
            if not user_team_id:
                print(f"⚠️  User {username} doesn't have team for account {account_name}, skipping...")
                continue
            
            # Update account
            cursor.execute('''
                UPDATE "Accounts"
                SET assigned_user_id = %s, team_id = %s
                WHERE account_id = %s
            ''', (sold_to_user_id, user_team_id, account_id))
            
            accounts_updated += 1
            print(f"✅ Updated account: {account_name} -> user: {username}, team: {user_team_id}")
        
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"📊 Accounts updated: {accounts_updated}")
        return True, accounts_updated
        
    except Exception as e:
        print(f"❌ Account data fix failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False, 0

def verify_migration():
    """Verify migration results"""
    try:
        print("\n🔍 Step 3: Verifying migration results...")
        
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        # Check users without teams
        cursor.execute('''
            SELECT COUNT(*) FROM "Users"
            WHERE team_id IS NULL AND is_deleted = 0
        ''')
        users_without_teams = cursor.fetchone()[0]
        
        # Check accounts with sold_to_user_id but missing assigned_user_id or team_id
        cursor.execute('''
            SELECT COUNT(*) FROM "Accounts"
            WHERE sold_to_user_id IS NOT NULL 
            AND (assigned_user_id IS NULL OR team_id IS NULL)
            AND is_deleted = 0
        ''')
        accounts_missing_data = cursor.fetchone()[0]
        
        # Check properly assigned marketplace accounts
        cursor.execute('''
            SELECT COUNT(*) FROM "Accounts"
            WHERE sold_to_user_id IS NOT NULL 
            AND assigned_user_id IS NOT NULL 
            AND team_id IS NOT NULL
            AND is_deleted = 0
        ''')
        properly_assigned = cursor.fetchone()[0]
        
        # Get team statistics
        cursor.execute('''
            SELECT COUNT(*) FROM "Teams"
        ''')
        total_teams = cursor.fetchone()[0]
        
        cursor.close()
        conn.close()
        
        print(f"📊 Verification Results:")
        print(f"   - Users without teams: {users_without_teams}")
        print(f"   - Accounts missing assigned_user_id/team_id: {accounts_missing_data}")
        print(f"   - Properly assigned marketplace accounts: {properly_assigned}")
        print(f"   - Total teams: {total_teams}")
        
        success = users_without_teams == 0 and accounts_missing_data == 0
        
        if success:
            print("✅ Migration verification PASSED")
        else:
            print("❌ Migration verification FAILED")
        
        return success
        
    except Exception as e:
        print(f"❌ Verification failed: {e}")
        return False

def main():
    """Main migration function"""
    print("🚀 Starting team features migration...")
    print(f"📅 Migration started at: {get_vn_time()}")
    print(f"🖥️  Database: {PG_CONFIG['host']}:{PG_CONFIG['port']}/{PG_CONFIG['database']}")
    print("="*60)
    
    # Test database connection
    if not test_database_connection():
        print("❌ Migration aborted due to database connection failure")
        return False
    
    # Step 1: Create teams for existing users
    success1, teams_created, users_updated = create_teams_for_existing_users()
    if not success1:
        print("❌ Migration aborted due to team creation failure")
        return False
    
    # Step 2: Fix sold accounts data
    success2, accounts_updated = fix_sold_accounts_data()
    if not success2:
        print("❌ Migration aborted due to account data fix failure")
        return False
    
    # Step 3: Verify results
    success3 = verify_migration()
    
    # Summary
    print("\n" + "="*60)
    print("📝 MIGRATION SUMMARY")
    print("="*60)
    print(f"✅ Teams created: {teams_created}")
    print(f"✅ Users updated: {users_updated}")
    print(f"✅ Accounts updated: {accounts_updated}")
    print(f"📅 Migration completed at: {get_vn_time()}")
    
    if success1 and success2 and success3:
        print("🎉 Migration completed successfully!")
        return True
    else:
        print("⚠️  Migration completed with issues. Please check the logs.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
