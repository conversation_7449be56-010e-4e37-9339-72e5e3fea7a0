{% extends "base_coreui.html" %}
{% block title %}<PERSON><PERSON><PERSON><PERSON> lý <PERSON>{% endblock %}

{% block head_extra %}
<!-- Select2 CSS -->
<link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />
<link href="https://cdn.jsdelivr.net/npm/select2-bootstrap-5-theme@1.3.0/dist/select2-bootstrap-5-theme.min.css" rel="stylesheet" />

<style>
    .card {
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
        border-radius: 0.25rem;
        margin-bottom: 1.5rem;
    }

    .card-header {
        background-color: #fff;
        border-bottom: 1px solid rgba(0, 0, 0, 0.125);
        padding: 0.75rem 1.25rem;
    }

    .btn-icon {
        display: inline-flex;
        align-items: center;
        justify-content: center;
    }

    .btn-icon i {
        margin-right: 0.25rem;
    }

    .spinning {
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }

    .table th {
        font-weight: 500;
        background-color: #ebedef;
    }

    .badge {
        font-size: 0.75rem;
        font-weight: 500;
        padding: 0.25em 0.5em;
    }

    .nav-tabs .nav-link {
        font-weight: 500;
        padding: 0.75rem 1rem;
    }

    .nav-tabs .nav-link.active {
        font-weight: 600;
        border-bottom: 2px solid #321fdb;
    }

    .stats-card {
        transition: all 0.3s;
    }

    .stats-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    }

    .stats-card .card-body {
        padding: 1rem;
    }

    .stats-card .stats-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
    }

    .stats-card .stats-number {
        font-size: 1.5rem;
        font-weight: 600;
    }

    .stats-card .stats-text {
        font-size: 0.875rem;
        color: #768192;
    }

    .highlight-card {
        border-left: 4px solid #2eb85c;
    }

    /* Fix Select2 dropdown trong modal */
    .select2-container--open .select2-dropdown {
        z-index: 1070 !important;
    }

    .select2-dropdown {
        z-index: 1070 !important;
    }

    /* Đảm bảo Select2 dropdown không bị che bởi modal */
    .select2-container--bootstrap-5.select2-container--open .select2-dropdown {
        z-index: 1070 !important;
    }
</style>
{% endblock %}

{% block content %}
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <strong>Quản lý tài khoản</strong>
        <div>
            <button class="btn btn-primary me-2" onclick="new coreui.Modal(document.getElementById('addAccountModal')).show()">
                <i class="cil-plus"></i> Thêm mới tài khoản
            </button>
            <button class="btn btn-success me-2" onclick="new coreui.Modal(document.getElementById('importAccountsModal')).show()">
                <i class="cil-cloud-upload"></i> Import tài khoản
            </button>
            {% if user_role == 'admin' %}
            <button class="btn btn-danger me-2" onclick="checkDuplicates()">
                <i class="cil-trash"></i> Loại bỏ trùng lặp
            </button>
            <button class="btn btn-warning" onclick="addUniqueConstraint()">
                <i class="cil-shield-alt"></i> Ngăn trùng lặp
            </button>
            {% endif %}
        </div>
    </div>
    <div class="card-body">
        <!-- Thống kê tài khoản -->
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-icon text-primary">
                            <i class="cil-people"></i>
                        </div>
                        <div class="stats-number" id="total-accounts">0</div>
                        <div class="stats-text">Tổng số tài khoản</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-icon text-success">
                            <i class="cil-leaf"></i>
                        </div>
                        <div class="stats-number" id="growing-accounts">0</div>
                        <div class="stats-text">Đang nuôi</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-icon text-info">
                            <i class="cil-check-circle"></i>
                        </div>
                        <div class="stats-number" id="qualified-accounts">0</div>
                        <div class="stats-text">Đủ điều kiện</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-icon text-warning">
                            <i class="cil-dollar"></i>
                        </div>
                        <div class="stats-number" id="aff-accounts">0</div>
                        <div class="stats-text">Có giỏ</div>
                    </div>
                </div>
            </div>
        </div>
        <div class="row mb-4">
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-icon text-secondary">
                            <i class="cil-warning"></i>
                        </div>
                        <div class="stats-number" id="failed-accounts">0</div>
                        <div class="stats-text">Bật hụt</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-icon text-dark">
                            <i class="cil-basket"></i>
                        </div>
                        <div class="stats-number" id="collected-accounts">0</div>
                        <div class="stats-text">Thu giỏ</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card">
                    <div class="card-body text-center">
                        <div class="stats-icon text-danger">
                            <i class="cil-x-circle"></i>
                        </div>
                        <div class="stats-number" id="dead-accounts">0</div>
                        <div class="stats-text">Die</div>
                    </div>
                </div>
            </div>
            <div class="col-md-3">
                <div class="card stats-card highlight-card">
                    <div class="card-body text-center">
                        <div class="stats-icon text-success">
                            <i class="cil-chart"></i>
                        </div>
                        <div class="stats-number" id="growing-1k-accounts">0</div>
                        <div class="stats-text">Đang nuôi ≥ 1K</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Bộ lọc cơ bản -->
        <div class="card mb-3">
            <div class="card-body">
                <div class="row g-3 align-items-center">
                    <div class="col-md-4">
                        <div class="input-group">
                            <span class="input-group-text"><i class="cil-search"></i></span>
                            <input type="text" class="form-control" id="search_name" placeholder="Tìm kiếm theo tên" value="{{ search_name }}">
                        </div>
                    </div>
                    {% if user_role != 'admin' %}
                    <div class="col-md-4">
                        <select class="form-select" id="team_filter">
                            <option value="">Tất cả đội</option>
                            {% for team in teams %}
                            <option value="{{ team[0] }}" {% if team[0]|string == team_filter %}selected{% endif %}>{{ team[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}
                    <div class="col-md-4">
                        <div class="d-flex gap-2 flex-wrap">
                            {% if user_role in ['admin', 'leader'] %}
                            <button class="btn btn-outline-primary btn-sm" id="advancedFilterBtn" onclick="toggleAdvancedFilter()">
                                <i class="cil-filter-alt"></i> Lọc nâng cao
                            </button>
                            {% endif %}
                            <button class="btn btn-primary btn-sm" onclick="filterAccounts(1)">
                                <i class="cil-filter"></i> Lọc
                            </button>
                            <button class="btn btn-info btn-sm" id="reloadDataBtn" onclick="reloadDataNoCookie()">
                                <i class="cil-reload"></i> Thống kê
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        {% if user_role in ['admin', 'leader'] %}
        <!-- Bộ lọc nâng cao -->
        <div class="card mb-3" id="advancedFilterCard" style="display: none;">
            <div class="card-header" id="advancedFilterHeader">
                <h6 class="mb-0">
                    <i class="cil-filter-alt"></i> Lọc nâng cao
                    <span class="badge bg-success ms-2" id="filterActiveIndicator" style="display: none;">Đang áp dụng</span>
                </h6>
            </div>
            <div class="card-body">
                <div class="row g-3">
                    <!-- Lọc theo follower -->
                    <div class="col-md-6">
                        <label class="form-label">Số follower</label>
                        <div class="row g-2">
                            <div class="col">
                                <input type="number" class="form-control" id="follower_min" placeholder="Từ" min="0"
                                       onkeypress="if(event.key==='Enter') applyAdvancedFilter()"
                                       onchange="applyAdvancedFilter()">
                            </div>
                            <div class="col-auto d-flex align-items-center">
                                <span>đến</span>
                            </div>
                            <div class="col">
                                <input type="number" class="form-control" id="follower_max" placeholder="Đến" min="0"
                                       onkeypress="if(event.key==='Enter') applyAdvancedFilter()"
                                       onchange="applyAdvancedFilter()">
                            </div>
                        </div>
                        <div class="mt-2">
                            <label class="form-label small">Lọc nhanh:</label>
                            <div class="btn-group w-100" role="group">
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setFollowerRange(0, 999)">
                                    Dưới 1K
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setFollowerRange(1000, 5000)">
                                    1K - 5K
                                </button>
                                <button type="button" class="btn btn-outline-primary btn-sm" onclick="setFollowerRange(5001, null)">
                                    Trên 5K
                                </button>
                                <button type="button" class="btn btn-outline-secondary btn-sm" onclick="clearFollowerRange()">
                                    Xóa
                                </button>
                            </div>
                        </div>
                    </div>

                    {% if user_role == 'admin' %}
                    <!-- Lọc theo team (multiple) - chỉ admin -->
                    <div class="col-md-6">
                        <label class="form-label">Đội</label>
                        <select class="form-select" id="advanced_team_filter" multiple>
                            {% for team in teams %}
                            <option value="{{ team[0] }}">{{ team[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    {% endif %}

                    <!-- Lọc theo trạng thái (multiple) -->
                    <div class="col-md-6">
                        <label class="form-label">Trạng thái</label>
                        <select class="form-select" id="advanced_status_filter" multiple>
                            <option value="Live">Live</option>
                            <option value="Die">Die</option>
                            <option value="Not Available">Not Available</option>
                            <option value="Có giỏ">Có giỏ</option>
                            <option value="Đang nuôi">Đang nuôi</option>
                            <option value="Đủ điều kiện">Đủ điều kiện</option>
                            <option value="Bật hụt">Bật hụt</option>
                            <option value="Thu giỏ">Thu giỏ</option>
                        </select>
                    </div>

                    <!-- Lọc theo nơi Login (multiple) -->
                    <div class="col-md-6">
                        <label class="form-label">Nơi Login</label>
                        <select class="form-select" id="advanced_logged_in_filter" multiple>
                            <option value="Phone">Phone</option>
                            <option value="Web">Web</option>
                            <option value="Phone+Web">Phone+Web</option>
                        </select>
                    </div>

                    <!-- Lọc theo ngày tạo -->
                    <div class="col-md-6">
                        <label class="form-label">Ngày tạo</label>
                        <div class="row g-2">
                            <div class="col">
                                <input type="date" class="form-control" id="date_added_from" placeholder="Từ ngày">
                            </div>
                            <div class="col-auto d-flex align-items-center">
                                <span>đến</span>
                            </div>
                            <div class="col">
                                <input type="date" class="form-control" id="date_added_to" placeholder="Đến ngày">
                            </div>
                        </div>
                    </div>
                </div>

                <div class="mt-3 d-flex gap-2">
                    <button class="btn btn-primary btn-lg" onclick="applyAdvancedFilter()"
                            title="Áp dụng các bộ lọc đã chọn">
                        <i class="cil-filter"></i> Áp dụng lọc
                    </button>
                    <button class="btn btn-outline-secondary" onclick="clearAdvancedFilter()"
                            title="Xóa tất cả bộ lọc và hiển thị toàn bộ danh sách">
                        <i class="cil-x"></i> Xóa bộ lọc
                    </button>
                    <button class="btn btn-outline-info" onclick="toggleAdvancedFilter()"
                            title="Ẩn bộ lọc nâng cao">
                        <i class="cil-chevron-top"></i> Ẩn
                    </button>
                </div>
            </div>
        </div>

        <!-- Panel hành động hàng loạt -->
        <div class="card mb-3" id="bulkActionsCard" style="display: none;">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <span class="fw-bold">Đã chọn: <span id="selectedCount">0</span> tài khoản</span>
                        <button class="btn btn-link btn-sm p-0 ms-2" onclick="selectAllInFilter()">Chọn tất cả</button>
                        <button class="btn btn-link btn-sm p-0 ms-2" onclick="clearSelection()">Bỏ chọn tất cả</button>
                    </div>
                    <div class="dropdown">
                        <button class="btn btn-success dropdown-toggle" type="button" id="bulkActionsDropdown" data-coreui-toggle="dropdown" aria-expanded="false">
                            <i class="cil-settings"></i> Hành động
                        </button>
                        <ul class="dropdown-menu" aria-labelledby="bulkActionsDropdown">
                            {% if user_role == 'admin' %}
                            <li><a class="dropdown-item" href="#" onclick="showBulkChangeTeam()"><i class="cil-people"></i> Thay đổi team</a></li>
                            {% endif %}
                            <li><a class="dropdown-item" href="#" onclick="showBulkChangeStatus()"><i class="cil-tags"></i> Thay đổi trạng thái</a></li>
                            <li><a class="dropdown-item" href="#" onclick="showBulkChangeLoggedIn()"><i class="cil-device-mobile"></i> Thay đổi nơi Login</a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="showExportModal()"><i class="cil-cloud-download"></i> Export danh sách</a></li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}

        <!-- Tab Navigation -->
        <ul class="nav nav-tabs mb-3" id="accountTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="all-tab" data-coreui-toggle="tab" data-coreui-target="#all" type="button" role="tab" aria-controls="all" aria-selected="true" onclick="setActiveTab('all')">
                    Tất cả
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="growing-tab" data-coreui-toggle="tab" data-coreui-target="#growing" type="button" role="tab" aria-controls="growing" aria-selected="false" onclick="setActiveTab('Đang nuôi')">
                    Đang nuôi
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="qualified-tab" data-coreui-toggle="tab" data-coreui-target="#qualified" type="button" role="tab" aria-controls="qualified" aria-selected="false" onclick="setActiveTab('Đủ điều kiện')">
                    Đủ điều kiện
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="aff-tab" data-coreui-toggle="tab" data-coreui-target="#aff" type="button" role="tab" aria-controls="aff" aria-selected="false" onclick="setActiveTab('Có giỏ')">
                    Có giỏ
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="failed-tab" data-coreui-toggle="tab" data-coreui-target="#failed" type="button" role="tab" aria-controls="failed" aria-selected="false" onclick="setActiveTab('Bật hụt')">
                    Bật hụt
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="collected-tab" data-coreui-toggle="tab" data-coreui-target="#collected" type="button" role="tab" aria-controls="collected" aria-selected="false" onclick="setActiveTab('Thu giỏ')">
                    Thu giỏ
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="dead-tab" data-coreui-toggle="tab" data-coreui-target="#dead" type="button" role="tab" aria-controls="dead" aria-selected="false" onclick="setActiveTab('Die')">
                    Die
                </button>
            </li>
            {% if user_role == 'admin' %}
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="sold-tab" data-coreui-toggle="tab" data-coreui-target="#sold" type="button" role="tab" aria-controls="sold" aria-selected="false" onclick="setActiveTab('Đã bán')">
                    Đã bán
                </button>
            </li>
            {% endif %}
        </ul>

        <!-- Hiển thị loading -->
        <div id="loading" style="display: none; text-align: center; margin: 20px 0;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Đang tải...</span>
            </div>
            <p>Đang tải dữ liệu...</p>
        </div>

        <!-- Progress bar cho việc cập nhật thông tin -->
        <div id="update-progress-container" style="display: none; margin: 20px 0;">
            <div class="d-flex justify-content-between mb-1">
                <span>Đang cập nhật thông tin tài khoản</span>
                <span id="update-progress-text">0/0</span>
            </div>
            <div class="progress" style="height: 20px;">
                <div id="update-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%;" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">0%</div>
            </div>
            <div id="update-current-account" class="mt-2 text-muted small"></div>
        </div>

        <!-- Hiển thị trạng thái hiện tại -->
        <div class="d-flex justify-content-between align-items-center mb-3">
            <div>
                <h5 class="m-0" id="current-status">Tất cả trạng thái</h5>
                <small class="text-muted" id="account-count">Đang tải...</small>
            </div>
            <div class="d-flex align-items-center">
                <span class="me-2">Sắp xếp theo:</span>
                <span class="badge bg-primary" id="current-sort">account_id ▲</span>
            </div>
        </div>

        <!-- Bảng dữ liệu -->
        <div class="table-responsive" id="regular-accounts-table">
            <table class="table table-striped table-hover border" id="accounts_table">
                <thead>
                    <tr>
                        {% if user_role in ['admin', 'leader'] %}
                        <th style="width: 40px;">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="selectAll" onchange="toggleSelectAll()">
                                <label class="form-check-label" for="selectAll"></label>
                            </div>
                        </th>
                        {% endif %}
                        <th data-sort="account_id" onclick="sortBy('account_id')" style="cursor: pointer">ID <span class="sort-indicator">▲</span></th>
                        <th data-sort="account_name" onclick="sortBy('account_name')" style="cursor: pointer">Tên</th>
                        <th>Link TikTok</th>
                        <th data-sort="team_id" onclick="sortBy('team_id')" style="cursor: pointer">Đội</th>
                        <th data-sort="assigned_user_id" onclick="sortBy('assigned_user_id')" style="cursor: pointer">Người phụ trách</th>
                        <th data-sort="status" onclick="sortBy('status')" style="cursor: pointer">Trạng thái</th>
                        {% if user_role in ['user', 'leader'] %}
                        <th style="width: 120px;">Check Revenue</th>
                        {% endif %}
                        <th data-sort="follower_count" onclick="sortBy('follower_count')" style="cursor: pointer">Follower</th>
                        <th data-sort="like_count" onclick="sortBy('like_count')" style="cursor: pointer">Likes</th>
                        <th data-sort="total_videos" onclick="sortBy('total_videos')" style="cursor: pointer">Total Videos</th>
                        <th data-sort="logged_in" onclick="sortBy('logged_in')" style="cursor: pointer">Nơi Login</th>
                        <th data-sort="date_added" onclick="sortBy('date_added')" style="cursor: pointer">Ngày tạo</th>
                        <th>Hành động</th>
                    </tr>
                </thead>
                <tbody id="accounts_body">
                    <!-- Dữ liệu sẽ được thêm bằng JavaScript -->
                </tbody>
            </table>
        </div>

        <!-- Bảng accounts đã bán (chỉ admin) -->
        {% if user_role == 'admin' %}
        <div class="table-responsive" id="sold-accounts-table" style="display: none;">
            <table class="table table-striped table-hover border" id="sold_accounts_table">
                <thead>
                    <tr>
                        <th data-sort="account_id" onclick="sortSoldBy('account_id')" style="cursor: pointer">ID <span class="sort-indicator">▲</span></th>
                        <th data-sort="account_name" onclick="sortSoldBy('account_name')" style="cursor: pointer">Tên Account</th>
                        <th>Link TikTok</th>
                        <th data-sort="follower_count" onclick="sortSoldBy('follower_count')" style="cursor: pointer">Follower</th>
                        <th data-sort="buyer_username" onclick="sortSoldBy('buyer_username')" style="cursor: pointer">Người mua</th>
                        <th data-sort="product_name" onclick="sortSoldBy('product_name')" style="cursor: pointer">Gói sản phẩm</th>
                        <th data-sort="order_number" onclick="sortSoldBy('order_number')" style="cursor: pointer">Đơn hàng</th>
                        <th data-sort="unit_price" onclick="sortSoldBy('unit_price')" style="cursor: pointer">Giá bán</th>
                        <th data-sort="sold_date" onclick="sortSoldBy('sold_date')" style="cursor: pointer">Ngày bán</th>
                        <th>Hành động</th>
                    </tr>
                </thead>
                <tbody id="sold_accounts_body">
                    <!-- Dữ liệu accounts đã bán sẽ được load bằng JavaScript -->
                </tbody>
            </table>
        </div>
        {% endif %}

        <!-- Phân trang -->
        <nav aria-label="Page navigation" id="pagination">
            <ul class="pagination justify-content-center">
                {% if page > 1 %}
                <li class="page-item">
                    <a class="page-link" href="#" onclick="filterAccounts({{ page - 1 }}); return false;">
                        <i class="cil-chevron-left"></i>
                    </a>
                </li>
                {% endif %}
                {% for p in range(1, total_pages + 1) %}
                <li class="page-item {% if p == page %}active{% endif %}">
                    <a class="page-link" href="#" onclick="filterAccounts({{ p }}); return false;">{{ p }}</a>
                </li>
                {% endfor %}
                {% if page < total_pages %}
                <li class="page-item">
                    <a class="page-link" href="#" onclick="filterAccounts({{ page + 1 }}); return false;">
                        <i class="cil-chevron-right"></i>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
    </div>
</div>

<!-- Modal Thêm tài khoản -->
<div class="modal fade" id="addAccountModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thêm tài khoản TikTok</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="addAccountForm" method="POST" action="/add_account">
                    <div class="mb-3">
                        <label class="form-label">Tên tài khoản</label>
                        <input type="text" class="form-control" name="account_name" placeholder="Tên tài khoản" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Cookie Data</label>
                        <textarea class="form-control" name="cookie_data" placeholder="Cookie Data" required rows="5"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-control" name="email" placeholder="Email của tài khoản">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Full Email</label>
                        <textarea class="form-control" name="full_email" placeholder="Thông tin email đầy đủ..." rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Full Info</label>
                        <textarea class="form-control" name="full_info" placeholder="Thông tin chi tiết khác..." rows="4"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Đội <span class="text-danger">*</span></label>
                        <select class="form-select" name="team_id" id="add_team_id" required>
                            <option value="">Chọn đội</option>
                            {% for team in teams %}
                            <option value="{{ team[0] }}">{{ team[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Người phụ trách</label>
                        <select class="form-select" name="assigned_user_id" id="add_assigned_user_id">
                            <option value="">Chọn người phụ trách (không bắt buộc)</option>
                            {% for user in users %}
                            <option value="{{ user[0] }}">{{ user[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="add_proxy_id" class="form-label">Proxy</label>
                        <select class="form-select" name="proxy_id" id="add_proxy_id">
                            <option value="">Chọn proxy (không bắt buộc)</option>
                            {% for proxy in proxies %}
                            <option value="{{ proxy[0] }}">{{ proxy[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="add_status" class="form-label">Trạng thái</label>
                        <select class="form-select" name="status" id="add_status" required>
                            <option value="Live">Live</option>
                            <option value="Die">Die</option>
                            <option value="Not Available" selected>Not Available</option>
                            <option value="Có giỏ">Có giỏ</option>
                            <option value="Đang nuôi">Đang nuôi</option>
                            <option value="Đủ điều kiện">Đủ điều kiện</option>
                            <option value="Bật hụt">Bật hụt</option>
                            <option value="Thu giỏ">Thu giỏ</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="add_logged_in" class="form-label">Nơi đang Login</label>
                        <select class="form-select" name="logged_in" id="add_logged_in" required>
                            <option value="Phone" selected>Phone</option>
                            <option value="Web">Web</option>
                            <option value="Phone+Web">Phone+Web</option>
                        </select>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-primary">Thêm</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal Chỉnh sửa tài khoản -->
<div class="modal fade" id="editAccountModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Chỉnh sửa tài khoản</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="editAccountForm" method="POST" action="/edit_account">
                    <input type="hidden" name="account_id" id="edit_account_id">
                    <div class="mb-3">
                        <label class="form-label">Tên tài khoản</label>
                        <input type="text" class="form-control" name="account_name" id="edit_account_name" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Cookie Data</label>
                        <textarea class="form-control" name="cookie_data" id="edit_cookie_data" required rows="5"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Email</label>
                        <input type="email" class="form-control" name="email" id="edit_email" placeholder="Email của tài khoản">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Full Email</label>
                        <textarea class="form-control" name="full_email" id="edit_full_email" placeholder="Thông tin email đầy đủ..." rows="3"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Full Info</label>
                        <textarea class="form-control" name="full_info" id="edit_full_info" placeholder="Thông tin chi tiết khác..." rows="4"></textarea>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Đội</label>
                        <select class="form-select" name="team_id" id="edit_team_id">
                            <option value="">Chọn đội (không bắt buộc)</option>
                            {% for team in teams %}
                            <option value="{{ team[0] }}">{{ team[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Người phụ trách</label>
                        <select class="form-select" name="assigned_user_id" id="edit_assigned_user_id">
                            <option value="">Chọn người phụ trách (không bắt buộc)</option>
                            {% for user in users %}
                            <option value="{{ user[0] }}">{{ user[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_proxy_id" class="form-label">Proxy</label>
                        <select class="form-select" name="proxy_id" id="edit_proxy_id">
                            <option value="">Chọn proxy (không bắt buộc)</option>
                            {% for proxy in proxies %}
                            <option value="{{ proxy[0] }}">{{ proxy[1] }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_status" class="form-label">Trạng thái</label>
                        <select class="form-select" name="status" id="edit_status" required>
                            <option value="Live">Live</option>
                            <option value="Die">Die</option>
                            <option value="Not Available">Not Available</option>
                            <option value="Có giỏ">Có giỏ</option>
                            <option value="Đang nuôi">Đang nuôi</option>
                            <option value="Đủ điều kiện">Đủ điều kiện</option>
                            <option value="Bật hụt">Bật hụt</option>
                            <option value="Thu giỏ">Thu giỏ</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="edit_logged_in" class="form-label">Nơi đang Login</label>
                        <select class="form-select" name="logged_in" id="edit_logged_in" required>
                            <option value="Phone">Phone</option>
                            <option value="Web">Web</option>
                            <option value="Phone+Web">Phone+Web</option>
                        </select>
                    </div>

                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-primary">Lưu</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Modal Import tài khoản -->
<div class="modal fade" id="importAccountsModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Import tài khoản từ Excel</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="importAccountsForm">
                    <div class="mb-3">
                        <label class="form-label">File Excel</label>
                        <input type="file" class="form-control" name="excel_file" id="excel_file" accept=".xlsx, .xls" required>
                        <div class="form-text">
                            <strong>Cột bắt buộc:</strong> username
                            <br>
                            <strong>Cột tùy chọn:</strong> team, trạng thái, email, full_email (hoặc "full email"), full_info (hoặc "full info")
                            <br>
                            <strong>Lưu ý:</strong> Nếu username đã tồn tại, hệ thống sẽ cập nhật các thông tin mới. Mặc định trạng thái là "Đang nuôi" nếu không có.
                            {% if user_role == 'leader' %}
                            <br>
                            <strong>Quyền hạn Leader:</strong>
                            <ul class="mb-0 mt-1">
                                <li>Chỉ được import vào team của mình</li>
                                <li>Không được cập nhật tài khoản từ team khác</li>
                                <li>Nếu cột team để trống, tài khoản sẽ được gán vào team của bạn</li>
                            </ul>
                            {% else %}
                            <br>
                            <strong>Quyền hạn Admin:</strong>
                            <ul class="mb-0 mt-1">
                                <li>Có thể tạo team mới nếu chưa tồn tại</li>
                                <li>Có thể cập nhật team cho tài khoản đã tồn tại (kể cả từ team khác)</li>
                                <li>Có thể import tài khoản vào bất kỳ team nào</li>
                                <li>Có thể overwrite dữ liệu từ các team khác</li>
                            </ul>
                            {% endif %}
                        </div>
                    </div>
                    <div class="text-end">
                        <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Hủy</button>
                        <button type="submit" class="btn btn-primary" id="importBtn">Import</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

{% if user_role == 'admin' %}
<!-- Modal thay đổi team hàng loạt -->
<div class="modal fade" id="bulkChangeTeamModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thay đổi team hàng loạt</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Bạn đang thay đổi team cho <strong><span id="bulkTeamCount">0</span></strong> tài khoản.</p>
                <div class="mb-3">
                    <label class="form-label">Chọn team mới</label>
                    <select class="form-select" id="bulkTeamSelect">
                        <option value="">Chọn team</option>
                        {% for team in teams %}
                        <option value="{{ team[0] }}">{{ team[1] }}</option>
                        {% endfor %}
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="executeBulkChangeTeam()">Thực hiện</button>
            </div>
        </div>
    </div>
</div>

{% endif %}

{% if user_role in ['admin', 'leader'] %}
<!-- Modal thay đổi trạng thái hàng loạt -->
<div class="modal fade" id="bulkChangeStatusModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thay đổi trạng thái hàng loạt</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Bạn đang thay đổi trạng thái cho <strong><span id="bulkStatusCount">0</span></strong> tài khoản.</p>
                <div class="mb-3">
                    <label class="form-label">Chọn trạng thái mới</label>
                    <select class="form-select" id="bulkStatusSelect">
                        <option value="">Chọn trạng thái</option>
                        <option value="Live">Live</option>
                        <option value="Die">Die</option>
                        <option value="Not Available">Not Available</option>
                        <option value="Có giỏ">Có giỏ</option>
                        <option value="Đang nuôi">Đang nuôi</option>
                        <option value="Đủ điều kiện">Đủ điều kiện</option>
                        <option value="Bật hụt">Bật hụt</option>
                        <option value="Thu giỏ">Thu giỏ</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="executeBulkChangeStatus()">Thực hiện</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal thay đổi nơi Login hàng loạt -->
<div class="modal fade" id="bulkChangeLoggedInModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Thay đổi nơi Login hàng loạt</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Bạn đang thay đổi nơi Login cho <strong><span id="bulkLoggedInCount">0</span></strong> tài khoản.</p>
                <div class="mb-3">
                    <label class="form-label">Chọn nơi Login mới</label>
                    <select class="form-select" id="bulkLoggedInSelect">
                        <option value="">Chọn nơi Login</option>
                        <option value="Phone">Phone</option>
                        <option value="Web">Web</option>
                        <option value="Phone+Web">Phone+Web</option>
                    </select>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-primary" onclick="executeBulkChangeLoggedIn()">Thực hiện</button>
            </div>
        </div>
    </div>
</div>

<!-- Modal export -->
<div class="modal fade" id="exportModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Export danh sách tài khoản</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p>Bạn đang export <strong><span id="exportCount">0</span></strong> tài khoản.</p>
                <div class="mb-3">
                    <label class="form-label">Chọn các cột để export</label>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="export_account_id" value="account_id" checked>
                                <label class="form-check-label" for="export_account_id">ID</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="export_account_name" value="account_name" checked>
                                <label class="form-check-label" for="export_account_name">Tên tài khoản</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="export_team_name" value="team_name" checked>
                                <label class="form-check-label" for="export_team_name">Đội</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="export_status" value="status" checked>
                                <label class="form-check-label" for="export_status">Trạng thái</label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="export_follower_count" value="follower_count" checked>
                                <label class="form-check-label" for="export_follower_count">Follower</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="export_like_count" value="like_count">
                                <label class="form-check-label" for="export_like_count">Likes</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="export_total_videos" value="total_videos">
                                <label class="form-check-label" for="export_total_videos">Total Videos</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="export_assigned_user" value="assigned_user">
                                <label class="form-check-label" for="export_assigned_user">Người phụ trách</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="export_logged_in" value="logged_in" checked>
                                <label class="form-check-label" for="export_logged_in">Nơi Login</label>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mb-3">
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="selectAllColumns" onchange="toggleAllColumns()">
                        <label class="form-check-label" for="selectAllColumns">Chọn tất cả</label>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Hủy</button>
                <button type="button" class="btn btn-success" onclick="executeExport()">
                    <i class="cil-cloud-download"></i> Export CSV
                </button>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Modal loại bỏ trùng lặp -->
<div class="modal fade" id="duplicatesModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Loại bỏ tài khoản trùng lặp</h5>
                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="duplicatesContent">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">Đang kiểm tra...</span>
                        </div>
                        <p class="mt-2">Đang kiểm tra tài khoản trùng lặp...</p>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Đóng</button>
                <button type="button" class="btn btn-danger" id="removeDuplicatesBtn" onclick="removeDuplicates()" style="display: none;">
                    <i class="cil-trash"></i> Loại bỏ trùng lặp
                </button>
            </div>
        </div>
    </div>
</div>

{% block scripts %}
<script>
    // Function format ngày tạo với múi giờ +7 và ngắn gọn
    function formatDateVN(dateString) {
        if (!dateString) return '';

        try {
            const date = new Date(dateString);
            // Chuyển sang múi giờ +7 (UTC+7)
            const vnDate = new Date(date.getTime() + (7 * 60 * 60 * 1000));

            // Format: DD/MM/YYYY HH:mm
            const day = vnDate.getUTCDate().toString().padStart(2, '0');
            const month = (vnDate.getUTCMonth() + 1).toString().padStart(2, '0');
            const year = vnDate.getUTCFullYear();
            const hours = vnDate.getUTCHours().toString().padStart(2, '0');
            const minutes = vnDate.getUTCMinutes().toString().padStart(2, '0');

            return `${day}/${month}/${year} ${hours}:${minutes}`;
        } catch (e) {
            return dateString;
        }
    }

    // Dữ liệu teams và users - sử dụng global variables để tránh duplicate declaration
    if (typeof window.teams === 'undefined') {
        window.teams = {{ teams|tojson }};
    }
    if (typeof window.users === 'undefined') {
        window.users = {{ users|tojson }};
    }

    $(document).ready(function() {
        // Xử lý form import tài khoản
        $('#importAccountsForm').on('submit', function(e) {
            e.preventDefault();

            const fileInput = document.getElementById('excel_file');
            if (!fileInput.files || fileInput.files.length === 0) {
                showToast('Vui lòng chọn file Excel', 'danger');
                return;
            }

            const file = fileInput.files[0];
            if (!file.name.endsWith('.xlsx') && !file.name.endsWith('.xls')) {
                showToast('File không đúng định dạng Excel', 'danger');
                return;
            }

            // Tạo FormData
            const formData = new FormData();
            formData.append('excel_file', file);

            // Hiển thị trạng thái đang xử lý
            const importBtn = document.getElementById('importBtn');
            const originalBtnText = importBtn.innerHTML;
            importBtn.disabled = true;
            importBtn.innerHTML = '<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> Đang xử lý...';

            // Gửi request
            fetch('/import_accounts', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                    // Đóng modal
                    coreui.Modal.getInstance(document.getElementById('importAccountsModal')).hide();
                    // Tải lại dữ liệu
                    filterAccounts(1);
                    loadAccountStats();
                } else {
                    showToast(data.error || 'Có lỗi xảy ra khi import tài khoản', 'danger');
                }
            })
            .catch(error => {
                console.error('Error importing accounts:', error);
                showToast('Có lỗi xảy ra khi import tài khoản: ' + error.message, 'danger');
            })
            .finally(() => {
                // Khôi phục trạng thái nút
                importBtn.disabled = false;
                importBtn.innerHTML = originalBtnText;
            });
        });

        // Không khởi tạo select2 cho modal - sử dụng select thông thường
        console.log('Modal selects will use regular Bootstrap dropdowns');

        // Tắt select2 trong modal để tránh vấn đề dropdown
        $('#addAccountModal').on('shown.bs.modal', function () {
            console.log('Add modal shown - using regular select instead of select2');

            // Destroy tất cả select2 trong modal
            $('#addAccountModal select').each(function() {
                if ($(this).hasClass('select2-hidden-accessible')) {
                    $(this).select2('destroy');
                }
            });

            // Không khởi tạo lại select2 - sử dụng select thông thường
            console.log('Using regular Bootstrap select dropdowns in modal');
        });

        // Xử lý form thêm tài khoản
        $('#addAccountForm').on('submit', function(e) {
            e.preventDefault();

            // Lấy dữ liệu form
            const formData = new FormData(this);

            // Gửi AJAX request
            $.ajax({
                url: '/add_account',
                method: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    // Nếu thành công, redirect sẽ xảy ra tự động
                    // Nhưng nếu có lỗi, sẽ trả về JSON
                    if (response && response.error) {
                        showToast(response.error, 'danger');
                    } else {
                        // Thành công - reload trang
                        window.location.reload();
                    }
                },
                error: function(xhr) {
                    console.error('Error:', xhr);
                    if (xhr.responseJSON && xhr.responseJSON.error) {
                        showToast(xhr.responseJSON.error, 'danger');
                    } else {
                        showToast('Có lỗi xảy ra khi thêm tài khoản', 'danger');
                    }
                }
            });
        });

        // Không khởi tạo select2 cho edit modal - sử dụng select thông thường
        console.log('Edit modal selects will use regular Bootstrap dropdowns');

        // Tắt select2 trong edit modal để tránh vấn đề dropdown
        $('#editAccountModal').on('shown.bs.modal', function () {
            console.log('Edit modal shown - using regular select instead of select2');

            // Destroy tất cả select2 trong modal
            $('#editAccountModal select').each(function() {
                if ($(this).hasClass('select2-hidden-accessible')) {
                    $(this).select2('destroy');
                }
            });

            // Thiết lập giá trị proxy với regular select
            if (window.currentProxyId) {
                console.log('Setting proxy_id with regular select:', window.currentProxyId);
                $('#edit_proxy_id').val(window.currentProxyId);
            }

            console.log('Using regular Bootstrap select dropdowns in modal');
        });

        // Select2 cho bộ lọc (không dùng theme để tránh conflict)
        $('#team_filter').select2({
            placeholder: "Tất cả đội",
            allowClear: true,
            width: '100%'
        });

        // Load dữ liệu ban đầu
        loadAccountStats();
        filterAccounts(1);
    });

    // Biến lưu trạng thái tab hiện tại
    let currentTab = 'all';

    // Hàm đặt tab hiện tại
    function setActiveTab(tabName) {
        console.log('Setting active tab to:', tabName);
        currentTab = tabName;

        // Cập nhật UI để hiển thị tab đang active
        document.querySelectorAll('#accountTabs .nav-link').forEach(tab => {
            tab.classList.remove('active');
            tab.setAttribute('aria-selected', 'false');
        });

        // Xác định ID của tab cần active
        let tabId;
        switch(tabName) {
            case 'all': tabId = 'all-tab'; break;
            case 'Đang nuôi': tabId = 'growing-tab'; break;
            case 'Đủ điều kiện': tabId = 'qualified-tab'; break;
            case 'Có giỏ': tabId = 'aff-tab'; break;
            case 'Bật hụt': tabId = 'failed-tab'; break;
            case 'Thu giỏ': tabId = 'collected-tab'; break;
            case 'Die': tabId = 'dead-tab'; break;
            case 'Đã bán': tabId = 'sold-tab'; break;
            default: tabId = 'all-tab';
        }

        // Show/hide appropriate table
        if (tabName === 'Đã bán') {
            document.getElementById('regular-accounts-table').style.display = 'none';
            {% if user_role == 'admin' %}
            document.getElementById('sold-accounts-table').style.display = 'block';
            {% endif %}
        } else {
            document.getElementById('regular-accounts-table').style.display = 'block';
            {% if user_role == 'admin' %}
            document.getElementById('sold-accounts-table').style.display = 'none';
            {% endif %}
        }

        // Active tab được chọn
        const activeTab = document.getElementById(tabId);
        if (activeTab) {
            activeTab.classList.add('active');
            activeTab.setAttribute('aria-selected', 'true');
        }

        // Hiển thị thông báo đang tải
        showLoading();

        // Tải dữ liệu mới với trạng thái đã chọn
        console.log('Filtering accounts with tab:', tabName);

        // Đặt lại các bộ lọc khác (chỉ khi không dùng advanced filter)
        {% if user_role in ['admin', 'leader'] %}
        if (typeof isAdvancedFilterActive !== 'undefined' && !isAdvancedFilterActive) {
            document.getElementById('search_name').value = '';
            const teamFilterElement = document.getElementById('team_filter');
            if (teamFilterElement) {
                $(teamFilterElement).val('').trigger('change');
            }
        }
        {% else %}
        document.getElementById('search_name').value = '';
        const teamFilterElement = document.getElementById('team_filter');
        if (teamFilterElement) {
            $(teamFilterElement).val('').trigger('change');
        }
        {% endif %}

        // Tải dữ liệu mới
        if (tabName === 'Đã bán') {
            {% if user_role == 'admin' %}
            loadSoldAccounts(1);
            {% endif %}
        } else {
            {% if user_role in ['admin', 'leader'] %}
            if (typeof isAdvancedFilterActive !== 'undefined' && isAdvancedFilterActive) {
                filterAccountsAdvanced(1);
            } else {
                filterAccounts(1);
            }
            {% else %}
            filterAccounts(1);
            {% endif %}
        }
    }

    // Functions for sold accounts (admin only)
    {% if user_role == 'admin' %}
    let soldAccountsCurrentPage = 1;
    let soldAccountsSortBy = 'sold_date';
    let soldAccountsSortOrder = 'desc';

    function loadSoldAccounts(page = 1) {
        console.log('🔍 Loading sold accounts, page:', page);
        soldAccountsCurrentPage = page;

        const url = `/api/accounts/sold?page=${page}&sort_by=${soldAccountsSortBy}&sort_order=${soldAccountsSortOrder}`;
        console.log('🔍 API URL:', url);

        fetch(url)
            .then(response => {
                console.log('🔍 Response status:', response.status);
                return response.json();
            })
            .then(data => {
                console.log('🔍 API Response:', data);
                if (data.success) {
                    console.log('✅ Found', data.accounts.length, 'sold accounts');
                    displaySoldAccounts(data.accounts);
                    updateSoldAccountsPagination(data.pagination);
                    updateAccountCount(`Đã bán: ${data.pagination.total} accounts`);
                } else {
                    console.error('❌ Error loading sold accounts:', data.error);
                    showError('Lỗi tải dữ liệu accounts đã bán: ' + data.error);
                }
            })
            .catch(error => {
                console.error('❌ Fetch error:', error);
                showError('Lỗi kết nối khi tải accounts đã bán');
            })
            .finally(() => {
                hideLoading();
            });
    }

    function displaySoldAccounts(accounts) {
        const tbody = document.getElementById('sold_accounts_body');
        tbody.innerHTML = '';

        if (accounts.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="10" class="text-center text-muted py-4">
                        <i class="cil-info"></i> Chưa có account nào được bán
                    </td>
                </tr>
            `;
            return;
        }

        accounts.forEach(account => {
            const row = document.createElement('tr');

            const tiktokLink = account.tiktok_url ?
                `<a href="${account.tiktok_url}" target="_blank" class="text-decoration-none">
                    <i class="cil-external-link"></i> TikTok
                </a>` :
                '<span class="text-muted">Không có</span>';

            const followerCount = account.follower_count ?
                account.follower_count.toLocaleString() :
                '<span class="text-muted">N/A</span>';

            const unitPrice = account.unit_price ?
                account.unit_price.toLocaleString() + ' MP' :
                '<span class="text-muted">N/A</span>';

            const soldDate = account.sold_date ?
                new Date(account.sold_date).toLocaleDateString('vi-VN') :
                '<span class="text-muted">N/A</span>';

            row.innerHTML = `
                <td>${account.account_id}</td>
                <td>
                    <strong>${account.account_name || 'N/A'}</strong>
                    ${account.username ? `<br><small class="text-muted">@${account.username}</small>` : ''}
                </td>
                <td>${tiktokLink}</td>
                <td>${followerCount}</td>
                <td>
                    <strong class="text-primary">${account.buyer_username || 'N/A'}</strong>
                    ${account.buyer_role ? `<br><small class="text-muted">${account.buyer_role}</small>` : ''}
                </td>
                <td>
                    <span class="badge bg-info">${account.product_name || 'N/A'}</span>
                    ${account.product_type ? `<br><small class="text-muted">${account.product_type}</small>` : ''}
                </td>
                <td>
                    <a href="/admin/marketplace/orders" class="text-decoration-none">
                        <strong>#${account.order_number || 'N/A'}</strong>
                    </a>
                </td>
                <td><strong class="text-success">${unitPrice}</strong></td>
                <td>${soldDate}</td>
                <td>
                    <div class="btn-group" role="group">
                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="viewAccountDetails(${account.account_id})" title="Xem chi tiết">
                            <i class="cil-info"></i>
                        </button>
                        <button type="button" class="btn btn-sm btn-outline-success" onclick="viewOrderDetails('${account.order_number}')" title="Xem đơn hàng">
                            <i class="cil-cart"></i>
                        </button>
                    </div>
                </td>
            `;

            tbody.appendChild(row);
        });
    }

    function sortSoldBy(column) {
        if (soldAccountsSortBy === column) {
            soldAccountsSortOrder = soldAccountsSortOrder === 'asc' ? 'desc' : 'asc';
        } else {
            soldAccountsSortBy = column;
            soldAccountsSortOrder = 'asc';
        }

        // Update sort indicators
        document.querySelectorAll('#sold_accounts_table .sort-indicator').forEach(indicator => {
            indicator.textContent = '';
        });

        const currentHeader = document.querySelector(`#sold_accounts_table th[data-sort="${column}"] .sort-indicator`);
        if (currentHeader) {
            currentHeader.textContent = soldAccountsSortOrder === 'asc' ? '▲' : '▼';
        }

        loadSoldAccounts(soldAccountsCurrentPage);
    }

    function updateSoldAccountsPagination(pagination) {
        const paginationContainer = document.getElementById('pagination');
        if (!paginationContainer) return;

        let paginationHTML = '<ul class="pagination justify-content-center">';

        // Previous button
        if (pagination.current_page > 1) {
            paginationHTML += `<li class="page-item">
                <a class="page-link" href="#" onclick="loadSoldAccounts(${pagination.current_page - 1}); return false;">Trước</a>
            </li>`;
        }

        // Page numbers
        for (let i = Math.max(1, pagination.current_page - 2); i <= Math.min(pagination.total_pages, pagination.current_page + 2); i++) {
            const activeClass = i === pagination.current_page ? 'active' : '';
            paginationHTML += `<li class="page-item">
                <a class="page-link" href="#" onclick="loadSoldAccounts(${i}); return false;">${i}</a>
            </li>`;
        }

        // Next button
        if (pagination.current_page < pagination.total_pages) {
            paginationHTML += `<li class="page-item">
                <a class="page-link" href="#" onclick="loadSoldAccounts(${pagination.current_page + 1}); return false;">Sau</a>
            </li>`;
        }

        paginationHTML += '</ul>';
        paginationContainer.innerHTML = paginationHTML;
    }

    function viewOrderDetails(orderNumber) {
        if (orderNumber && orderNumber !== 'N/A') {
            window.open(`/admin/marketplace/orders?search=${orderNumber}`, '_blank');
        }
    }
    {% endif %}

    function updateAccountCount(text) {
        const accountCountElement = document.getElementById('account-count');
        if (accountCountElement) {
            accountCountElement.textContent = text;
        }
    }

    function showError(message) {
        console.error('Error:', message);
        alert('Lỗi: ' + message);
    }

    function showLoading() {
        document.getElementById('loading').style.display = 'block';
    }

    function hideLoading() {
        document.getElementById('loading').style.display = 'none';
    }

    // Hàm tải thống kê tài khoản
    function loadAccountStats() {
        console.log('Loading account stats...');
        // Thêm timestamp để tránh cache
        const timestamp = new Date().getTime();
        fetch(`/api/accounts/stats?t=${timestamp}`)
            .then(response => response.json())
            .then(data => {
                console.log('Account stats response:', data);
                if (data.success) {
                    console.log('Updating UI with stats:', {
                        total: data.total,
                        growing: data.growing,
                        qualified: data.qualified,
                        aff: data.aff,
                        failed: data.failed,
                        collected: data.collected,
                        dead: data.dead,
                        growing_1k: data.growing_1k
                    });
                    document.getElementById('total-accounts').textContent = data.total || 0;
                    document.getElementById('growing-accounts').textContent = data.growing || 0;
                    document.getElementById('qualified-accounts').textContent = data.qualified || 0;
                    document.getElementById('aff-accounts').textContent = data.aff || 0;
                    document.getElementById('failed-accounts').textContent = data.failed || 0;
                    document.getElementById('collected-accounts').textContent = data.collected || 0;
                    document.getElementById('dead-accounts').textContent = data.dead || 0;
                    document.getElementById('growing-1k-accounts').textContent = data.growing_1k || 0;
                } else {
                    console.error('Stats API returned error:', data.message);
                }
            })
            .catch(error => {
                console.error('Error loading account stats:', error);
            });
    }

    function editAccount(accountId) {
        fetch(`/get_account/${accountId}`)
            .then(response => response.json())
            .then(data => {
                console.log('Account data from server:', data);

                // Reset form
                $('#editAccountForm')[0].reset();

                // Set basic fields
                document.getElementById('edit_account_id').value = data.account_id;
                document.getElementById('edit_account_name').value = data.account_name;
                document.getElementById('edit_cookie_data').value = data.cookie_data;
                document.getElementById('edit_email').value = data.email || '';
                document.getElementById('edit_full_email').value = data.full_email || '';
                document.getElementById('edit_full_info').value = data.full_info || '';
                document.getElementById('edit_logged_in').value = data.logged_in || 'Phone';

                // Chỉ set is_rentable nếu element tồn tại
                const isRentableElement = document.getElementById('edit_is_rentable');
                if (isRentableElement) {
                    isRentableElement.checked = data.is_rentable;
                }

                // Lưu proxy_id để sử dụng sau khi modal được hiển thị
                window.currentProxyId = data.proxy_id || '';
                console.log('Saved proxy_id for later use:', window.currentProxyId);
                console.log('Type of proxy_id:', typeof window.currentProxyId);

                // Nếu proxy_id là số, chuyển đổi thành chuỗi
                if (typeof window.currentProxyId === 'number') {
                    window.currentProxyId = window.currentProxyId.toString();
                    console.log('Converted proxy_id to string:', window.currentProxyId);
                }

                // Set Select2 fields
                $('#edit_team_id').val(data.team_id || '').trigger('change');
                $('#edit_assigned_user_id').val(data.assigned_user_id || '').trigger('change');
                $('#edit_status').val(data.status).trigger('change');

                // Show modal
                new coreui.Modal(document.getElementById('editAccountModal')).show();
            })
            .catch(error => {
                console.error('Error fetching account data:', error);
                showToast('Có lỗi xảy ra khi lấy dữ liệu tài khoản', 'danger');
            });
    }

    // Xử lý form submit
    $('#editAccountForm').on('submit', function(e) {
        e.preventDefault();

        // Lấy giá trị proxy_id
        let proxyId = $('#edit_proxy_id').val();
        console.log('Raw proxy_id from select:', proxyId);

        // Nếu không có giá trị hoặc giá trị rỗng, đặt thành chuỗi rỗng
        if (!proxyId) {
            proxyId = '';
            console.log('Empty proxy_id detected, setting to empty string');
        }

        // Get all form data
        const formData = {
            account_id: $('#edit_account_id').val(),
            account_name: $('#edit_account_name').val(),
            cookie_data: $('#edit_cookie_data').val(),
            email: $('#edit_email').val() || '',
            full_email: $('#edit_full_email').val() || '',
            full_info: $('#edit_full_info').val() || '',
            team_id: $('#edit_team_id').val() || '',
            assigned_user_id: $('#edit_assigned_user_id').val() || '',
            status: $('#edit_status').val(),
            logged_in: $('#edit_logged_in').val() || 'Phone',
            proxy_id: proxyId
        };

        // Chỉ thêm is_rentable nếu element tồn tại
        const isRentableElement = $('#edit_is_rentable');
        if (isRentableElement.length > 0) {
            formData.is_rentable = isRentableElement.is(':checked') ? '1' : '0';
        }

        // Log detailed information
        console.log('Form data to be submitted:', formData);
        console.log('Proxy ID element:', document.getElementById('edit_proxy_id'));
        console.log('Select2 data:', $('#edit_proxy_id').select2('data'));

        $.ajax({
            url: '/edit_account',
            method: 'POST',
            data: formData,
            success: function(response) {
                if (response.error) {
                    showToast(response.error, 'danger');
                } else {
                    // Log debug info
                    if (response.debug) {
                        console.log('Debug info:', response.debug);
                    }

                    showToast(response.message || 'Cập nhật tài khoản thành công', 'success');
                    coreui.Modal.getInstance(document.getElementById('editAccountModal')).hide();
                    filterAccounts(1);
                    loadAccountStats();
                }
            },
            error: function(xhr) {
                console.error('Error:', xhr);
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    showToast(xhr.responseJSON.error, 'danger');
                } else {
                    showToast('Có lỗi xảy ra khi cập nhật tài khoản', 'danger');
                }
            }
        });
    });



    // Hàm hiển thị thông báo sử dụng CoreUI Toast
    function showToast(message, type = 'success') {
        const toastContainer = document.getElementById('toast-container');
        if (!toastContainer) {
            // Tạo container nếu chưa tồn tại
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed top-0 end-0 p-3';
            document.body.appendChild(container);
        }

        // Tạo ID duy nhất cho toast
        const toastId = 'toast-' + Date.now();

        // Tạo HTML cho toast
        const toastHtml = `
            <div class="toast align-items-center text-white bg-${type} border-0" role="alert" aria-live="assertive" aria-atomic="true" id="${toastId}">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-coreui-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>
        `;

        // Thêm toast vào container
        document.getElementById('toast-container').innerHTML += toastHtml;

        // Khởi tạo và hiển thị toast
        const toastElement = document.getElementById(toastId);
        const toast = new coreui.Toast(toastElement, {
            autohide: true,
            delay: 5000
        });
        toast.show();

        // Xóa toast sau khi ẩn
        toastElement.addEventListener('hidden.coreui.toast', function() {
            toastElement.remove();
        });
    }

    // Hàm hiển thị modal xác nhận sử dụng CoreUI Modal
    function showConfirmModal(title, message, confirmCallback) {
        // Kiểm tra xem modal đã tồn tại chưa
        let confirmModal = document.getElementById('confirmModal');

        if (!confirmModal) {
            // Tạo modal nếu chưa tồn tại
            const modalHtml = `
                <div class="modal fade" id="confirmModal" tabindex="-1" aria-labelledby="confirmModalLabel" aria-hidden="true">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="confirmModalLabel"></h5>
                                <button type="button" class="btn-close" data-coreui-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body"></div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-coreui-dismiss="modal">Hủy</button>
                                <button type="button" class="btn btn-danger" id="confirmModalYesBtn">Xác nhận</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // Thêm modal vào body
            document.body.insertAdjacentHTML('beforeend', modalHtml);
            confirmModal = document.getElementById('confirmModal');
        }

        // Cập nhật nội dung modal
        document.getElementById('confirmModalLabel').textContent = title;
        confirmModal.querySelector('.modal-body').textContent = message;

        // Xử lý sự kiện nút xác nhận
        const yesButton = document.getElementById('confirmModalYesBtn');

        // Xóa event listener cũ (nếu có)
        const newYesButton = yesButton.cloneNode(true);
        yesButton.parentNode.replaceChild(newYesButton, yesButton);

        // Thêm event listener mới
        newYesButton.addEventListener('click', function() {
            // Ẩn modal
            const modalInstance = coreui.Modal.getInstance(confirmModal);
            modalInstance.hide();

            // Gọi callback
            confirmCallback();
        });

        // Hiển thị modal
        const modal = new coreui.Modal(confirmModal);
        modal.show();
    }

    function deleteAccount(accountId) {
        showConfirmModal(
            'Xác nhận xóa',
            'Bạn có chắc muốn xóa tài khoản này không?',
            function() {
                fetch(`/delete_account/${accountId}`, { method: 'POST' })
                    .then(response => response.json())
                    .then(data => {
                        if (data.message) {
                            showToast(data.message, 'success');
                            filterAccounts(1);
                            loadAccountStats();
                        }
                    })
                    .catch(error => {
                        showToast("Lỗi: " + error, 'danger');
                    });
            }
        );
    }

    let isReloading = false;
    let accountsToUpdate = [];
    let currentBatchIndex = 0;
    let totalUpdated = 0;
    const BATCH_SIZE = 20;

    function showProgressBar(total) {
        document.getElementById('update-progress-container').style.display = 'block';
        document.getElementById('update-progress-text').textContent = `0/${total}`;
        document.getElementById('update-progress-bar').style.width = '0%';
        document.getElementById('update-progress-bar').textContent = '0%';
        document.getElementById('update-progress-bar').setAttribute('aria-valuenow', 0);
        document.getElementById('update-current-account').textContent = '';
    }

    function updateProgressBar(current, total) {
        const percent = Math.round((current / total) * 100);
        document.getElementById('update-progress-text').textContent = `${current}/${total}`;
        document.getElementById('update-progress-bar').style.width = `${percent}%`;
        document.getElementById('update-progress-bar').textContent = `${percent}%`;
        document.getElementById('update-progress-bar').setAttribute('aria-valuenow', percent);
    }

    function hideProgressBar() {
        document.getElementById('update-progress-container').style.display = 'none';
    }

    function reloadDataNoCookie() {
        if (isReloading) {
            showToast("Đang tải dữ liệu, vui lòng đợi...", "warning");
            return;
        }

        // Kiểm tra xem có tài khoản nào được chọn không (chỉ cho admin/leader)
        const isAdmin = '{{ user_role }}' === 'admin' || '{{ user_role }}' === 'leader';
        let selectedAccountIds = [];

        if (isAdmin && typeof selectedAccounts !== 'undefined') {
            selectedAccountIds = Array.from(selectedAccounts);
        }

        if (selectedAccountIds.length > 0) {
            // Có tài khoản được chọn - thống kê chỉ những tài khoản đó
            startUpdateProcess(selectedAccountIds, `Bắt đầu cập nhật ${selectedAccountIds.length} tài khoản đã chọn`);
        } else {
            // Không có tài khoản nào được chọn - hỏi có muốn thống kê tất cả không
            showConfirmModal(
                'Xác nhận thống kê',
                'Bạn chưa chọn tài khoản nào. Bạn có muốn thống kê tất cả tài khoản theo điều kiện lọc hiện tại không?',
                function() {
                    // Lấy tất cả tài khoản theo điều kiện lọc hiện tại
                    fetchAccountsByCurrentFilter();
                }
            );
        }
    }

    function fetchAccountsByCurrentFilter() {
        isReloading = true;
        document.getElementById('reloadDataBtn').disabled = true;
        document.getElementById('reloadDataBtn').innerHTML = '<i class="cil-reload spinning"></i> Đang tải...';

        // Tạo URL với điều kiện lọc hiện tại
        let url, params;

        if (typeof isAdvancedFilterActive !== 'undefined' && isAdvancedFilterActive) {
            // Sử dụng advanced filter
            params = new URLSearchParams({
                all: 'true',
                search_name: document.getElementById('search_name').value,
                sort: sortColumn,
                direction: sortDirection,
                advanced: 'true'
            });

            // Thêm tab filter cho advanced filter
            if (typeof currentTab !== 'undefined' && currentTab !== 'all') {
                params.append('tab_status', currentTab);
            }

            const followerMin = document.getElementById('follower_min').value;
            const followerMax = document.getElementById('follower_max').value;
            const dateAddedFrom = document.getElementById('date_added_from').value;
            const dateAddedTo = document.getElementById('date_added_to').value;
            {% if user_role == 'admin' %}
            const selectedTeams = $('#advanced_team_filter').val() || [];
            {% else %}
            const selectedTeams = [];
            {% endif %}
            const selectedStatuses = $('#advanced_status_filter').val() || [];
            const selectedLoggedIn = $('#advanced_logged_in_filter').val() || [];

            if (followerMin) params.append('follower_min', followerMin);
            if (followerMax) params.append('follower_max', followerMax);
            if (dateAddedFrom) params.append('date_added_from', dateAddedFrom);
            if (dateAddedTo) params.append('date_added_to', dateAddedTo);
            selectedTeams.forEach(team => params.append('teams', team));
            selectedStatuses.forEach(status => params.append('statuses', status));
            selectedLoggedIn.forEach(loggedIn => params.append('logged_in_values', loggedIn));

            url = `/accounts?${params.toString()}`;
        } else {
            // Sử dụng basic filter
            params = new URLSearchParams({
                all: 'true',
                search_name: document.getElementById('search_name').value,
                sort: sortColumn,
                direction: sortDirection
            });

            const teamFilter = document.getElementById('team_filter')?.value || '';
            if (currentTab !== 'all') params.append('status', currentTab);
            if (teamFilter) params.append('team_id', teamFilter);

            url = `/accounts?${params.toString()}`;
        }

        console.log('Fetching accounts with current filter:', url);

        fetch(url, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
        .then(response => response.json())
        .then(data => {
            console.log('Received filtered data:', data);

            if (data.error) {
                showToast(data.error, "danger");
                resetUpdateState();
                return;
            }

            if (!data.accounts || data.accounts.length === 0) {
                showToast("Không có tài khoản nào phù hợp với bộ lọc hiện tại", "warning");
                resetUpdateState();
                return;
            }

            // Lấy danh sách account IDs
            const accountIds = data.accounts.map(account => {
                // Kiểm tra xem account là object hay array
                if (Array.isArray(account)) {
                    return account[0]; // account_id từ array
                } else if (typeof account === 'object' && account.account_id) {
                    return account.account_id; // account_id từ object
                }
                return null;
            }).filter(id => id !== null);

            startUpdateProcess(accountIds, `Bắt đầu cập nhật ${accountIds.length} tài khoản theo điều kiện lọc`);
        })
        .catch(error => {
            console.error("Lỗi khi lấy danh sách tài khoản:", error);
            showToast("Lỗi khi lấy danh sách tài khoản: " + error.message, "danger");
            resetUpdateState();
        });
    }

    function startUpdateProcess(accountIds, message) {
        accountsToUpdate = accountIds.map(id => ({ account_id: id }));
        totalUpdated = 0;
        currentBatchIndex = 0;

        console.log(`Starting update process for ${accountsToUpdate.length} accounts`);
        showProgressBar(accountsToUpdate.length);
        showToast(message, "info");

        // Bắt đầu cập nhật theo batch
        updateNextBatch();
    }

    function updateNextBatch() {
        if (currentBatchIndex >= accountsToUpdate.length) {
            // Đã hoàn thành tất cả
            finishUpdate();
            return;
        }

        const endIndex = Math.min(currentBatchIndex + BATCH_SIZE, accountsToUpdate.length);
        const currentBatch = accountsToUpdate.slice(currentBatchIndex, endIndex);

        // Cập nhật thông tin hiện tại
        const accountNames = currentBatch.map(acc => acc.account_name || `ID: ${acc.account_id}`).join(', ');
        document.getElementById('update-current-account').textContent = `Đang cập nhật: ${accountNames}`;

        // Gửi yêu cầu cập nhật batch hiện tại
        fetch('/update_batch_account_stats', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                account_ids: currentBatch.map(acc => acc.account_id)
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.error) {
                console.error("Lỗi khi cập nhật batch:", data.error);
                showToast(`Lỗi khi cập nhật: ${data.error}`, "warning");
            } else {
                // Cập nhật số lượng đã hoàn thành
                totalUpdated += data.updated_count;
                updateProgressBar(totalUpdated, accountsToUpdate.length);

                // Cập nhật dữ liệu trong bảng nếu có
                if (data.updated_accounts && data.updated_accounts.length > 0) {
                    updateAccountsInTable(data.updated_accounts);
                }
            }

            // Chuyển sang batch tiếp theo
            currentBatchIndex = endIndex;
            setTimeout(updateNextBatch, 500); // Đợi 0.5 giây trước khi tiếp tục
        })
        .catch(error => {
            console.error("Lỗi khi cập nhật batch:", error);
            showToast(`Lỗi khi cập nhật: ${error.message}`, "danger");
            // Vẫn tiếp tục với batch tiếp theo
            currentBatchIndex = endIndex;
            setTimeout(updateNextBatch, 500);
        });
    }

    function updateAccountsInTable(updatedAccounts) {
        // Lấy tất cả các hàng trong bảng
        const rows = document.querySelectorAll('#accounts_table tbody tr');

        // Cập nhật từng hàng nếu tìm thấy tài khoản tương ứng
        updatedAccounts.forEach(account => {
            for (let i = 0; i < rows.length; i++) {
                const cells = rows[i].querySelectorAll('td');

                // Xác định vị trí cột ID dựa trên user role
                const hasCheckbox = {{ 'true' if user_role in ['admin', 'leader'] else 'false' }};
                const idColumnIndex = hasCheckbox ? 1 : 0; // Cột ID

                // Kiểm tra account_id
                if (cells[idColumnIndex] && cells[idColumnIndex].textContent.trim() == account.account_id) {
                    // Tính vị trí các cột dữ liệu
                    const hasRevenue = {{ 'true' if user_role in ['user', 'leader'] else 'false' }};
                    let followerIndex, likeIndex, videoIndex;

                    if (hasCheckbox && hasRevenue) {
                        // admin/leader có checkbox + revenue: checkbox(0) + ID(1) + name(2) + link(3) + team(4) + user(5) + status(6) + revenue(7) + follower(8) + like(9) + video(10)
                        followerIndex = 8;
                        likeIndex = 9;
                        videoIndex = 10;
                    } else if (hasCheckbox && !hasRevenue) {
                        // admin không có revenue: checkbox(0) + ID(1) + name(2) + link(3) + team(4) + user(5) + status(6) + follower(7) + like(8) + video(9)
                        followerIndex = 7;
                        likeIndex = 8;
                        videoIndex = 9;
                    } else if (!hasCheckbox && hasRevenue) {
                        // user/leader không có checkbox: ID(0) + name(1) + link(2) + team(3) + user(4) + status(5) + revenue(6) + follower(7) + like(8) + video(9)
                        followerIndex = 7;
                        likeIndex = 8;
                        videoIndex = 9;
                    } else {
                        // user không có checkbox và revenue: ID(0) + name(1) + link(2) + team(3) + user(4) + status(5) + follower(6) + like(7) + video(8)
                        followerIndex = 6;
                        likeIndex = 7;
                        videoIndex = 8;
                    }

                    // Cập nhật dữ liệu với format số
                    if (cells[followerIndex]) {
                        cells[followerIndex].textContent = (account.follower_count || 0).toLocaleString();
                    }
                    if (cells[likeIndex]) {
                        cells[likeIndex].textContent = (account.like_count || 0).toLocaleString();
                    }
                    if (cells[videoIndex]) {
                        cells[videoIndex].textContent = account.total_videos || 0;
                    }

                    // Thêm hiệu ứng highlight
                    rows[i].style.backgroundColor = '#d4edda';
                    setTimeout(() => {
                        rows[i].style.backgroundColor = '';
                    }, 2000);

                    break;
                }
            }
        });
    }

    function finishUpdate() {
        // Cập nhật thống kê
        loadAccountStats();

        // Hiển thị thông báo hoàn thành
        showToast(`Đã cập nhật thông tin cho ${totalUpdated}/${accountsToUpdate.length} tài khoản`, "success");

        // Reset trạng thái
        resetUpdateState();
    }

    function resetUpdateState() {
        isReloading = false;
        document.getElementById('reloadDataBtn').disabled = false;
        document.getElementById('reloadDataBtn').innerHTML = '<i class="cil-reload"></i> Thống kê thông tin';
        hideProgressBar();
    }

    let isRefreshing = {};
    function refreshAccount(accountId) {
        if (isRefreshing[accountId]) {
            showToast("Đang tải dữ liệu cho tài khoản này, vui lòng đợi...", "warning");
            return;
        }
        isRefreshing[accountId] = true;
        const refreshBtn = document.querySelector(`button[onclick="refreshAccount('${accountId}')"]`);
        refreshBtn.disabled = true;
        refreshBtn.innerHTML = '<i class="cil-reload spinning"></i>';

        // Sử dụng API batch với một tài khoản
        fetch('/update_batch_account_stats', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                account_ids: [accountId]
            })
        })
        .then(response => response.json())
        .then(data => {
            console.log('RefreshAccount response:', data); // Debug log
            if (data.error) {
                showToast(data.error, "danger");
            } else {
                // Cập nhật dữ liệu trong bảng nếu có
                if (data.updated_accounts && data.updated_accounts.length > 0) {
                    console.log('Updating accounts in table:', data.updated_accounts); // Debug log
                    updateAccountsInTable(data.updated_accounts);
                } else {
                    console.log('No updated_accounts in response'); // Debug log
                }

                // Cập nhật thống kê
                loadAccountStats();

                // Hiển thị thông báo
                showToast(data.message, "success");
            }
        })
        .catch(error => {
            console.error("Lỗi khi tải dữ liệu:", error);
            showToast("Lỗi khi tải dữ liệu: " + error.message, "danger");
        })
        .finally(() => {
            isRefreshing[accountId] = false;
            refreshBtn.disabled = false;
            refreshBtn.innerHTML = '<i class="cil-reload"></i>';
        });
    }

    // Biến lưu trạng thái sắp xếp
    let sortColumn = 'account_id';
    let sortDirection = 'asc';

    // Không khai báo lại currentTab ở đây vì đã khai báo ở dòng 561

    function filterAccounts(page) {
        showLoading();
        const searchName = document.getElementById('search_name').value;
        const teamFilter = document.getElementById('team_filter').value;
        let statusFilter = '';

        // Đặt statusFilter dựa trên tab hiện tại
        if (currentTab !== 'all') {
            statusFilter = currentTab;
        }

        console.log('Filtering accounts with status:', statusFilter);
        console.log('Current sort column:', sortColumn, 'direction:', sortDirection);

        // Tạo URL với các tham số
        const url = new URL(window.location.origin + '/accounts');
        url.searchParams.append('page', page);
        url.searchParams.append('search_name', searchName);
        url.searchParams.append('status', statusFilter);
        url.searchParams.append('team_id', teamFilter);
        url.searchParams.append('sort', sortColumn);
        url.searchParams.append('direction', sortDirection);

        console.log('Request URL:', url.toString());

        fetch(url.toString(), {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
        .then(response => {
            console.log('Response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('Received data:', data);

            // Kiểm tra xem có dữ liệu không
            if (!data.accounts || data.accounts.length === 0) {
                document.getElementById('accounts_body').innerHTML = `
                    <tr>
                        <td colspan="11" class="text-center">Không có dữ liệu cho trạng thái "${statusFilter || 'Tất cả'}"</td>
                    </tr>`;
                hideLoading();
                updatePagination(data.page || 1, data.total_pages || 1);
                return;
            }

            let html = '';
            data.accounts.forEach(account => {
                html += `
                    <tr>
                        <td>${account.account_id}</td>
                        <td>${account.account_name}</td>
                        <td><a href="https://www.tiktok.com/@${account.account_name}" target="_blank">@${account.account_name}</a></td>
                        <td>${getTeamName(account.team_id)}</td>
                        <td>${getUserName(account.assigned_user_id)}</td>
                        <td>${account.status}</td>
                        {% if user_role in ['user', 'leader'] %}
                        <td>
                            <div class="form-check form-switch">
                                <input class="form-check-input revenue-toggle" type="checkbox"
                                       id="revenue_${account.account_id}"
                                       ${account.revenue_enabled ? 'checked' : ''}
                                       ${(!account.cookie_data || account.cookie_data.trim() === '') ? 'disabled title="Account chưa có cookie"' : ''}
                                       onchange="toggleRevenue(${account.account_id}, this.checked)">
                                <label class="form-check-label" for="revenue_${account.account_id}"></label>
                            </div>
                            ${(!account.cookie_data || account.cookie_data.trim() === '') ? '<small class="text-muted">Chưa có cookie</small>' : ''}
                        </td>
                        {% endif %}
                        <td>${account.follower_count || 0}</td>
                        <td>${account.like_count || 0}</td>
                        <td>${account.total_videos || 0}</td>
                        <td>${account.logged_in || 'Phone'}</td>
                        <td>${formatDateVN(account.date_added)}</td>
                        <td>
                            <div class="btn-group" style="gap: 4px;">
                                <button class="btn btn-info btn-sm" onclick="refreshAccount('${account.account_id}')" title="Refresh Data">
                                    <i class="cil-reload"></i>
                                </button>
                                <button class="btn btn-warning btn-sm" onclick="editAccount('${account.account_id}')">
                                    <i class="cil-pencil"></i>
                                </button>
                                <button class="btn btn-danger btn-sm" onclick="deleteAccount('${account.account_id}')">
                                    <i class="cil-trash"></i>
                                </button>
                            </div>
                        </td>
                    </tr>`;
            });
            document.getElementById('accounts_body').innerHTML = html;

            // Cập nhật phân trang
            updatePagination(data.page || 1, data.total_pages || 1);

            // Cập nhật chỉ báo sắp xếp
            updateSortIndicators();

            // Hiển thị thông báo trạng thái hiện tại
            const statusText = statusFilter ? `Trạng thái: ${statusFilter}` : 'Tất cả trạng thái';
            document.getElementById('current-status').textContent = statusText;

            // Cập nhật số lượng account
            const accountCountElement = document.getElementById('account-count');
            if (data.total_accounts !== undefined) {
                const currentPage = data.page || 1;
                const accountsOnPage = data.accounts ? data.accounts.length : 0;
                const startIndex = ((currentPage - 1) * 20) + 1;
                const endIndex = startIndex + accountsOnPage - 1;

                if (accountsOnPage > 0) {
                    accountCountElement.textContent = `Hiển thị ${startIndex}-${endIndex} trong tổng số ${data.total_accounts} tài khoản`;
                } else {
                    accountCountElement.textContent = `Không có tài khoản nào`;
                }
            }

            hideLoading();
        })
        .catch(error => {
            console.error("Lỗi khi lọc tài khoản:", error);
            showToast("Lỗi khi lọc tài khoản: " + error.message, "danger");
            hideLoading();
        });
    }

    // Hàm lấy tên đội và người dùng từ dữ liệu ban đầu
    function getTeamName(teamId) {
        if (!teamId) return '';
        const team = window.teams.find(t => t[0] == teamId);
        return team ? team[1] : '';
    }

    function getUserName(userId) {
        if (!userId) return '';
        const user = window.users.find(u => u[0] == userId);
        return user ? user[1] : '';
    }

    // Hàm cập nhật phân trang
    function updatePagination(currentPage, totalPages) {
        // Xác định hàm filter nào sẽ được sử dụng
        {% if user_role == 'admin' %}
        const filterFunction = (typeof isAdvancedFilterActive !== 'undefined' && isAdvancedFilterActive)
            ? 'filterAccountsAdvanced' : 'filterAccounts';
        {% else %}
        const filterFunction = 'filterAccounts';
        {% endif %}

        let paginationHtml = '';
        if (currentPage > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="${filterFunction}(${currentPage - 1}); return false;">
                        <i class="cil-chevron-left"></i>
                    </a>
                </li>`;
        }

        // Hiển thị tối đa 5 trang
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);

        if (startPage > 1) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="${filterFunction}(1); return false;">1</a>
                </li>`;

            if (startPage > 2) {
                paginationHtml += `
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>`;
            }
        }

        for (let i = startPage; i <= endPage; i++) {
            paginationHtml += `
                <li class="page-item ${i === currentPage ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="${filterFunction}(${i}); return false;">${i}</a>
                </li>`;
        }

        if (endPage < totalPages) {
            if (endPage < totalPages - 1) {
                paginationHtml += `
                    <li class="page-item disabled">
                        <span class="page-link">...</span>
                    </li>`;
            }

            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="${filterFunction}(${totalPages}); return false;">${totalPages}</a>
                </li>`;
        }

        if (currentPage < totalPages) {
            paginationHtml += `
                <li class="page-item">
                    <a class="page-link" href="#" onclick="${filterFunction}(${currentPage + 1}); return false;">
                        <i class="cil-chevron-right"></i>
                    </a>
                </li>`;
        }

        document.getElementById('pagination').innerHTML = `<ul class="pagination justify-content-center">${paginationHtml}</ul>`;
    }

    // Hàm sắp xếp dữ liệu theo cột
    function sortBy(column) {
        console.log('Sorting by column:', column);

        if (sortColumn === column) {
            // Đảo chiều sắp xếp nếu đã sắp xếp theo cột này
            sortDirection = sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            // Đặt cột mới và hướng mặc định là tăng dần
            sortColumn = column;
            sortDirection = 'asc';
        }

        console.log(`Sort direction is now: ${sortDirection}`);

        // Cập nhật giao diện
        updateSortIndicators();

        // Tải lại dữ liệu với filter phù hợp
        {% if user_role == 'admin' %}
        if (typeof isAdvancedFilterActive !== 'undefined' && isAdvancedFilterActive) {
            filterAccountsAdvanced(1);
        } else {
            filterAccounts(1);
        }
        {% else %}
        filterAccounts(1);
        {% endif %}
    }

    // Mapping từ column ID sang tên hiển thị
    const columnDisplayNames = {
        'account_id': 'ID',
        'account_name': 'Tên',
        'team_id': 'Đội',
        'assigned_user_id': 'Người phụ trách',
        'status': 'Trạng thái',
        'follower_count': 'Follower',
        'like_count': 'Likes',
        'total_videos': 'Total Videos',
        'date_added': 'Ngày thêm',
        'last_updated': 'Cập nhật cuối',
        'logged_in': 'Nơi Login',
        'buyer_username': 'Người mua',
        'sold_at': 'Ngày bán'
    };

    // Hàm cập nhật chỉ báo sắp xếp
    function updateSortIndicators() {
        // Xóa tất cả chỉ báo sắp xếp
        document.querySelectorAll('th .sort-indicator').forEach(el => el.remove());

        // Thêm chỉ báo cho cột đang sắp xếp
        const th = document.querySelector(`th[data-sort="${sortColumn}"]`);
        if (th) {
            const indicator = document.createElement('span');
            indicator.className = 'sort-indicator ms-1';
            indicator.innerHTML = sortDirection === 'asc' ? '▲' : '▼';
            th.appendChild(indicator);

            // Cập nhật hiển thị trạng thái sắp xếp với tên column đẹp
            const columnDisplayName = columnDisplayNames[sortColumn] || sortColumn;
            const directionIcon = sortDirection === 'asc' ? '▲' : '▼';
            document.getElementById('current-sort').textContent = `${columnDisplayName} ${directionIcon}`;
        }
    }

    // Xử lý form import tài khoản
    $('#importAccountsForm').on('submit', function(e) {
        e.preventDefault();

        const formData = new FormData(this);

        $.ajax({
            url: '/import_accounts',
            method: 'POST',
            data: formData,
            contentType: false,
            processData: false,
            success: function(response) {
                if (response.error) {
                    alert(response.error);
                } else {
                    alert(response.message || 'Import tài khoản thành công');
                    coreui.Modal.getInstance(document.getElementById('importAccountsModal')).hide();
                    filterAccounts(1);
                    loadAccountStats();
                }
            },
            error: function(xhr) {
                console.error('Error:', xhr);
                if (xhr.responseJSON && xhr.responseJSON.error) {
                    alert(xhr.responseJSON.error);
                } else {
                    alert('Có lỗi xảy ra khi import tài khoản');
                }
            }
        });
    });

    // ===== ADVANCED FEATURES FOR ADMIN AND LEADER =====
    {% if user_role in ['admin', 'leader'] %}

    // Biến lưu trạng thái
    let selectedAccounts = new Set();
    let allAccountsInFilter = [];
    let isAdvancedFilterActive = false;

    // Khởi tạo Select2 cho bộ lọc nâng cao
    $(document).ready(function() {
        {% if user_role == 'admin' %}
        $('#advanced_team_filter').select2({
            placeholder: "Chọn đội",
            allowClear: true,
            width: '100%'
        });
        {% endif %}

        $('#advanced_status_filter').select2({
            placeholder: "Chọn trạng thái",
            allowClear: true,
            width: '100%'
        });

        $('#advanced_logged_in_filter').select2({
            placeholder: "Chọn nơi Login",
            allowClear: true,
            width: '100%'
        });
    });

    // Toggle bộ lọc nâng cao
    function toggleAdvancedFilter() {
        const card = document.getElementById('advancedFilterCard');
        const btn = document.getElementById('advancedFilterBtn');

        if (card.style.display === 'none') {
            card.style.display = 'block';
            btn.classList.remove('btn-outline-primary');
            btn.classList.add('btn-primary');
            btn.innerHTML = '<i class="cil-filter-alt"></i> Đang lọc nâng cao';
        } else {
            card.style.display = 'none';
            btn.classList.remove('btn-primary');
            btn.classList.add('btn-outline-primary');
            btn.innerHTML = '<i class="cil-filter-alt"></i> Lọc nâng cao';
            clearAdvancedFilter();
        }
    }

    // Áp dụng bộ lọc nâng cao
    function applyAdvancedFilter() {
        console.log('🔍 applyAdvancedFilter() called');

        // Kiểm tra giá trị input
        const followerMin = document.getElementById('follower_min').value;
        const followerMax = document.getElementById('follower_max').value;
        console.log('🔍 Follower range:', followerMin, 'to', followerMax);

        // Hiển thị thông báo
        let filterInfo = [];
        if (followerMin) filterInfo.push(`Từ ${followerMin} followers`);
        if (followerMax) filterInfo.push(`Đến ${followerMax} followers`);

        if (filterInfo.length > 0) {
            showToast(`Đang áp dụng lọc: ${filterInfo.join(', ')}`, 'info');
        } else {
            showToast('Đang áp dụng lọc nâng cao...', 'info');
        }

        isAdvancedFilterActive = true;

        // Hiển thị indicator
        document.getElementById('filterActiveIndicator').style.display = 'inline';

        filterAccountsAdvanced(1);
    }

    // Xóa bộ lọc nâng cao
    function clearAdvancedFilter() {
        document.getElementById('follower_min').value = '';
        document.getElementById('follower_max').value = '';
        document.getElementById('date_added_from').value = '';
        document.getElementById('date_added_to').value = '';
        {% if user_role == 'admin' %}
        $('#advanced_team_filter').val(null).trigger('change');
        {% endif %}
        $('#advanced_status_filter').val(null).trigger('change');
        $('#advanced_logged_in_filter').val(null).trigger('change');

        // Xóa highlight của các nút lọc nhanh follower
        const followerBtnGroup = document.querySelector('.col-md-6 .btn-group');
        if (followerBtnGroup) {
            const buttons = followerBtnGroup.querySelectorAll('button');
            buttons.forEach(btn => btn.classList.remove('active'));
        }

        isAdvancedFilterActive = false;

        // Ẩn indicator
        document.getElementById('filterActiveIndicator').style.display = 'none';

        filterAccounts(1);
    }

    // Functions cho lọc nhanh follower
    function setFollowerRange(min, max) {
        document.getElementById('follower_min').value = min;
        document.getElementById('follower_max').value = max || '';

        // Highlight nút được chọn (chỉ trong follower filter)
        const followerBtnGroup = document.querySelector('.col-md-6 .btn-group');
        const buttons = followerBtnGroup.querySelectorAll('button');
        buttons.forEach(btn => btn.classList.remove('active'));

        // Tìm và highlight nút được click
        if (min === 0 && max === 999) {
            buttons[0].classList.add('active');
        } else if (min === 1000 && max === 5000) {
            buttons[1].classList.add('active');
        } else if (min === 5001 && max === null) {
            buttons[2].classList.add('active');
        }

        showToast(`Đã đặt lọc follower: ${min}${max ? ' - ' + max : '+'}`, 'info');

        // Tự động áp dụng filter
        applyAdvancedFilter();
    }

    function clearFollowerRange() {
        document.getElementById('follower_min').value = '';
        document.getElementById('follower_max').value = '';

        // Xóa highlight tất cả nút (chỉ trong follower filter)
        const followerBtnGroup = document.querySelector('.col-md-6 .btn-group');
        const buttons = followerBtnGroup.querySelectorAll('button');
        buttons.forEach(btn => btn.classList.remove('active'));

        showToast('Đã xóa lọc follower', 'info');
    }

    // Hàm lọc nâng cao
    function filterAccountsAdvanced(page) {
        console.log('🔍 filterAccountsAdvanced() called with page:', page);
        showLoading();

        const searchName = document.getElementById('search_name').value;
        const followerMin = document.getElementById('follower_min').value;
        const followerMax = document.getElementById('follower_max').value;

        console.log('🔍 Advanced filter values:', {
            searchName,
            followerMin,
            followerMax
        });
        {% if user_role == 'admin' %}
        const selectedTeams = $('#advanced_team_filter').val() || [];
        {% else %}
        const selectedTeams = [];
        {% endif %}
        const selectedStatuses = $('#advanced_status_filter').val() || [];
        const selectedLoggedIn = $('#advanced_logged_in_filter').val() || [];
        const dateAddedFrom = document.getElementById('date_added_from').value;
        const dateAddedTo = document.getElementById('date_added_to').value;

        const params = new URLSearchParams({
            page: page,
            search_name: searchName,
            sort: sortColumn,
            direction: sortDirection,
            advanced: 'true'
        });

        // Thêm tab filter nếu không phải "all"
        if (typeof currentTab !== 'undefined' && currentTab !== 'all') {
            params.append('tab_status', currentTab);
        }

        if (followerMin) params.append('follower_min', followerMin);
        if (followerMax) params.append('follower_max', followerMax);
        if (dateAddedFrom) params.append('date_added_from', dateAddedFrom);
        if (dateAddedTo) params.append('date_added_to', dateAddedTo);
        selectedTeams.forEach(team => params.append('teams', team));
        selectedStatuses.forEach(status => params.append('statuses', status));
        selectedLoggedIn.forEach(loggedIn => params.append('logged_in_values', loggedIn));

        fetch(`/accounts?${params.toString()}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        })
        .then(response => response.json())
        .then(data => {
            renderAccountsTable(data);
            updatePagination(data.page || 1, data.total_pages || 1);

            // Cập nhật thông tin tab
            const statusText = currentTab !== 'all' ? `Trạng thái: ${currentTab}` : 'Tất cả trạng thái';
            const statusElement = document.getElementById('current-status');
            if (statusElement) {
                statusElement.textContent = statusText + ' (Lọc nâng cao)';
            }

            hideLoading();
        })
        .catch(error => {
            console.error("Lỗi khi lọc nâng cao:", error);
            showToast("Lỗi khi lọc nâng cao: " + error.message, "danger");
            hideLoading();
        });
    }

    // Render bảng tài khoản với checkbox (cho admin) hoặc không (cho non-admin)
    function renderAccountsTable(data) {
        const isAdmin = '{{ user_role }}' === 'admin' || '{{ user_role }}' === 'leader';
        const colspanCount = isAdmin ? 13 : 12;

        // Cập nhật số lượng account
        const accountCountElement = document.getElementById('account-count');
        if (data.total_accounts !== undefined) {
            const currentPage = data.page || 1;
            const totalPages = data.total_pages || 1;
            const accountsOnPage = data.accounts ? data.accounts.length : 0;
            const startIndex = ((currentPage - 1) * 20) + 1;
            const endIndex = startIndex + accountsOnPage - 1;

            if (accountsOnPage > 0) {
                accountCountElement.textContent = `Hiển thị ${startIndex}-${endIndex} trong tổng số ${data.total_accounts} tài khoản`;
            } else {
                accountCountElement.textContent = `Không có tài khoản nào`;
            }
        }

        if (!data.accounts || data.accounts.length === 0) {
            document.getElementById('accounts_body').innerHTML = `
                <tr>
                    <td colspan="${colspanCount}" class="text-center">Không có dữ liệu</td>
                </tr>`;
            return;
        }

        let html = '';
        data.accounts.forEach(account => {
            html += `<tr>`;

            // Chỉ thêm checkbox cho admin
            if (isAdmin) {
                const accountId = parseInt(account.account_id);
                const isChecked = selectedAccounts.has(accountId);
                html += `
                    <td>
                        <div class="form-check">
                            <input class="form-check-input account-checkbox" type="checkbox"
                                   value="${accountId}" ${isChecked ? 'checked' : ''}
                                   onchange="toggleAccountSelection(${accountId})">
                        </div>
                    </td>`;
            }

            html += `
                    <td>${account.account_id}</td>
                    <td>${account.account_name}</td>
                    <td><a href="https://www.tiktok.com/@${account.account_name}" target="_blank">@${account.account_name}</a></td>
                    <td>${getTeamName(account.team_id)}</td>
                    <td>${getUserName(account.assigned_user_id)}</td>
                    <td>${account.status}</td>
                    {% if user_role in ['user', 'leader'] %}
                    <td>
                        <div class="form-check form-switch">
                            <input class="form-check-input revenue-toggle" type="checkbox"
                                   id="revenue_${account.account_id}"
                                   ${account.revenue_enabled ? 'checked' : ''}
                                   ${(!account.cookie_data || account.cookie_data.trim() === '') ? 'disabled title="Account chưa có cookie"' : ''}
                                   onchange="toggleRevenue(${account.account_id}, this.checked)">
                            <label class="form-check-label" for="revenue_${account.account_id}"></label>
                        </div>
                        ${(!account.cookie_data || account.cookie_data.trim() === '') ? '<small class="text-muted">Chưa có cookie</small>' : ''}
                    </td>
                    {% endif %}
                    <td>${account.follower_count || 0}</td>
                    <td>${account.like_count || 0}</td>
                    <td>${account.total_videos || 0}</td>
                    <td><span class="badge bg-info">${account.logged_in || 'Phone'}</span></td>
                    <td>${formatDateVN(account.date_added)}</td>
                    <td>
                        <div class="btn-group" style="gap: 4px;">
                            <button class="btn btn-info btn-sm" onclick="refreshAccount('${account.account_id}')" title="Refresh Data">
                                <i class="cil-reload"></i>
                            </button>
                            <button class="btn btn-warning btn-sm" onclick="editAccount('${account.account_id}')">
                                <i class="cil-pencil"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="deleteAccount('${account.account_id}')">
                                <i class="cil-trash"></i>
                            </button>
                        </div>
                    </td>
                </tr>`;
        });
        document.getElementById('accounts_body').innerHTML = html;

        // Chỉ cập nhật select all cho admin
        if (isAdmin) {
            updateSelectAllState();
            updateBulkActionsVisibility();
        }
    }

    // Toggle chọn tài khoản
    function toggleAccountSelection(accountId) {
        // Ensure accountId is integer
        const id = parseInt(accountId);

        if (selectedAccounts.has(id)) {
            selectedAccounts.delete(id);
        } else {
            selectedAccounts.add(id);
        }

        updateSelectAllState();
        updateBulkActionsVisibility();
    }

    // Toggle select all
    function toggleSelectAll() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const accountCheckboxes = document.querySelectorAll('.account-checkbox');

        if (selectAllCheckbox.checked) {
            // Chọn tất cả tài khoản trong trang hiện tại
            accountCheckboxes.forEach(cb => {
                cb.checked = true;
                selectedAccounts.add(parseInt(cb.value)); // Convert to integer
            });
        } else {
            // Bỏ chọn tất cả trong trang hiện tại
            accountCheckboxes.forEach(cb => {
                cb.checked = false;
                selectedAccounts.delete(parseInt(cb.value)); // Convert to integer
            });
        }

        updateBulkActionsVisibility();
    }

    // Lấy tất cả tài khoản trong filter hiện tại
    async function fetchAllAccountsInFilter() {
        const params = new URLSearchParams({
            all: 'true',
            search_name: document.getElementById('search_name').value,
            sort: sortColumn,
            direction: sortDirection
        });

        if (isAdvancedFilterActive) {
            params.append('advanced', 'true');

            // Thêm tab filter cho advanced filter
            if (typeof currentTab !== 'undefined' && currentTab !== 'all') {
                params.append('tab_status', currentTab);
            }

            const followerMin = document.getElementById('follower_min').value;
            const followerMax = document.getElementById('follower_max').value;
            const dateAddedFrom = document.getElementById('date_added_from').value;
            const dateAddedTo = document.getElementById('date_added_to').value;
            {% if user_role == 'admin' %}
            const selectedTeams = $('#advanced_team_filter').val() || [];
            {% else %}
            const selectedTeams = [];
            {% endif %}
            const selectedStatuses = $('#advanced_status_filter').val() || [];
            const selectedLoggedIn = $('#advanced_logged_in_filter').val() || [];

            if (followerMin) params.append('follower_min', followerMin);
            if (followerMax) params.append('follower_max', followerMax);
            if (dateAddedFrom) params.append('date_added_from', dateAddedFrom);
            if (dateAddedTo) params.append('date_added_to', dateAddedTo);
            selectedTeams.forEach(team => params.append('teams', team));
            selectedStatuses.forEach(status => params.append('statuses', status));
            selectedLoggedIn.forEach(loggedIn => params.append('logged_in_values', loggedIn));
        } else {
            const teamFilter = document.getElementById('team_filter')?.value || '';
            if (currentTab !== 'all') params.append('status', currentTab);
            if (teamFilter) params.append('team_id', teamFilter);
        }

        const response = await fetch(`/accounts?${params.toString()}`, {
            headers: { 'X-Requested-With': 'XMLHttpRequest' }
        });
        const data = await response.json();
        return data.accounts || [];
    }

    // Cập nhật trạng thái select all
    function updateSelectAllState() {
        const selectAllCheckbox = document.getElementById('selectAll');
        const accountCheckboxes = document.querySelectorAll('.account-checkbox');
        const checkedCount = document.querySelectorAll('.account-checkbox:checked').length;

        if (checkedCount === 0) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = false;
        } else if (checkedCount === accountCheckboxes.length) {
            selectAllCheckbox.indeterminate = false;
            selectAllCheckbox.checked = true;
        } else {
            selectAllCheckbox.indeterminate = true;
        }
    }

    // Cập nhật hiển thị panel hành động
    function updateBulkActionsVisibility() {
        const count = selectedAccounts.size;
        const panel = document.getElementById('bulkActionsCard');
        const countSpan = document.getElementById('selectedCount');

        if (count > 0) {
            panel.style.display = 'block';
            countSpan.textContent = count;
        } else {
            panel.style.display = 'none';
        }
    }

    // Xóa selection
    function clearSelection() {
        selectedAccounts.clear();
        document.querySelectorAll('.account-checkbox').forEach(cb => cb.checked = false);
        const selectAllCheckbox = document.getElementById('selectAll');
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        }
        updateBulkActionsVisibility();
    }

    // Chọn tất cả accounts trong filter hiện tại
    async function selectAllInFilter() {
        try {
            showToast('Đang tải tất cả tài khoản trong filter...', 'info');

            const allAccounts = await fetchAllAccountsInFilter();

            // Clear selection hiện tại
            selectedAccounts.clear();

            // Thêm tất cả account IDs vào selection
            allAccounts.forEach(account => {
                // Kiểm tra xem account là object hay array
                let accountId;
                if (Array.isArray(account)) {
                    accountId = account[0]; // account_id từ array
                } else if (typeof account === 'object' && account.account_id) {
                    accountId = account.account_id; // account_id từ object
                } else {
                    return;
                }

                selectedAccounts.add(parseInt(accountId));
            });

            // Cập nhật UI - check tất cả checkbox hiện tại trên trang
            document.querySelectorAll('.account-checkbox').forEach(cb => {
                const accountId = parseInt(cb.value);
                if (selectedAccounts.has(accountId)) {
                    cb.checked = true;
                } else {
                    cb.checked = false;
                }
            });

            // Cập nhật select all checkbox
            const selectAllCheckbox = document.getElementById('selectAll');
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = true;
                selectAllCheckbox.indeterminate = false;
            }

            updateBulkActionsVisibility();

            showToast(`Đã chọn ${selectedAccounts.size} tài khoản`, 'success');
        } catch (error) {
            console.error('Error selecting all accounts:', error);
            showToast('Lỗi khi chọn tất cả tài khoản: ' + error.message, 'danger');
        }
    }

    // Hiển thị modal thay đổi team
    function showBulkChangeTeam() {
        document.getElementById('bulkTeamCount').textContent = selectedAccounts.size;
        new coreui.Modal(document.getElementById('bulkChangeTeamModal')).show();
    }

    // Hiển thị modal thay đổi trạng thái
    function showBulkChangeStatus() {
        document.getElementById('bulkStatusCount').textContent = selectedAccounts.size;
        new coreui.Modal(document.getElementById('bulkChangeStatusModal')).show();
    }

    // Hiển thị modal thay đổi nơi Login
    function showBulkChangeLoggedIn() {
        document.getElementById('bulkLoggedInCount').textContent = selectedAccounts.size;
        new coreui.Modal(document.getElementById('bulkChangeLoggedInModal')).show();
    }

    // Hàm kiểm tra tài khoản trùng lặp
    function checkDuplicates() {
        const modal = new coreui.Modal(document.getElementById('duplicatesModal'));
        modal.show();

        // Reset modal content
        document.getElementById('duplicatesContent').innerHTML = `
            <div class="text-center">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">Đang kiểm tra...</span>
                </div>
                <p class="mt-2">Đang kiểm tra tài khoản trùng lặp...</p>
            </div>
        `;
        document.getElementById('removeDuplicatesBtn').style.display = 'none';

        // Gọi API kiểm tra trùng lặp
        fetch('/api/accounts/check-duplicates')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    displayDuplicates(data);
                } else {
                    document.getElementById('duplicatesContent').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="cil-warning"></i> Lỗi: ${data.error}
                        </div>
                    `;
                }
            })
            .catch(error => {
                console.error('Error checking duplicates:', error);
                document.getElementById('duplicatesContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="cil-warning"></i> Lỗi khi kiểm tra trùng lặp: ${error.message}
                    </div>
                `;
            });
    }

    // Hàm hiển thị danh sách trùng lặp
    function displayDuplicates(data) {
        const content = document.getElementById('duplicatesContent');
        const removeDuplicatesBtn = document.getElementById('removeDuplicatesBtn');

        if (data.duplicate_groups === 0) {
            content.innerHTML = `
                <div class="alert alert-success">
                    <i class="cil-check-circle"></i> Không tìm thấy tài khoản trùng lặp!
                </div>
            `;
            removeDuplicatesBtn.style.display = 'none';
            return;
        }

        let html = `
            <div class="alert alert-warning">
                <i class="cil-warning"></i> Tìm thấy <strong>${data.duplicate_groups}</strong> nhóm tài khoản trùng lặp
                (tổng cộng <strong>${data.total_duplicates}</strong> tài khoản sẽ bị xóa)
            </div>
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Tên tài khoản</th>
                            <th>Số lượng trùng</th>
                            <th>Chi tiết</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        data.duplicates.forEach(duplicate => {
            html += `
                <tr>
                    <td><strong>${duplicate.account_name}</strong></td>
                    <td><span class="badge bg-warning">${duplicate.count}</span></td>
                    <td>
                        <div class="small">
            `;

            duplicate.accounts.forEach((account, index) => {
                const isKeep = index === duplicate.accounts.length - 1; // Giữ lại tài khoản cuối cùng (ID lớn nhất)
                const badgeClass = isKeep ? 'bg-success' : 'bg-danger';
                const action = isKeep ? 'Giữ lại' : 'Sẽ xóa';

                html += `
                    <div class="d-flex justify-content-between align-items-center mb-1">
                        <span>ID: ${account.id} | Team: ${account.team || 'N/A'} | ${formatDateVN(account.date_added)}</span>
                        <span class="badge ${badgeClass}">${action}</span>
                    </div>
                `;
            });

            html += `
                        </div>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
            <div class="alert alert-info">
                <i class="cil-info"></i> <strong>Quy tắc:</strong> Hệ thống sẽ giữ lại tài khoản có ID lớn nhất (mới nhất) và xóa các tài khoản còn lại.
            </div>
        `;

        content.innerHTML = html;
        removeDuplicatesBtn.style.display = 'inline-block';
    }

    // Hàm loại bỏ trùng lặp
    function removeDuplicates() {
        if (!confirm('Bạn có chắc chắn muốn loại bỏ các tài khoản trùng lặp? Hành động này không thể hoàn tác!')) {
            return;
        }

        const removeDuplicatesBtn = document.getElementById('removeDuplicatesBtn');
        removeDuplicatesBtn.disabled = true;
        removeDuplicatesBtn.innerHTML = '<i class="spinner-border spinner-border-sm"></i> Đang xử lý...';

        fetch('/api/accounts/remove-duplicates', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    let successHtml = `
                        <div class="alert alert-success">
                            <i class="cil-check-circle"></i> Đã loại bỏ thành công ${data.removed_count} tài khoản trùng lặp từ ${data.duplicate_groups} nhóm!
                        </div>
                        <div class="mt-3">
                            <h6>Chi tiết:</h6>
                            <ul class="list-group">
                    `;

                    data.duplicates.forEach(duplicate => {
                        successHtml += `
                            <li class="list-group-item d-flex justify-content-between align-items-center">
                                ${duplicate.account_name}
                                <span class="badge bg-success">Giữ ID ${duplicate.kept_id}, xóa ${duplicate.removed_count} tài khoản</span>
                            </li>
                        `;
                    });

                    successHtml += `
                            </ul>
                        </div>
                    `;

                    document.getElementById('duplicatesContent').innerHTML = successHtml;
                    removeDuplicatesBtn.style.display = 'none';

                    // Reload dữ liệu tài khoản
                    setTimeout(() => {
                        filterAccounts(currentPage);
                    }, 2000);

                } else {
                    document.getElementById('duplicatesContent').innerHTML = `
                        <div class="alert alert-danger">
                            <i class="cil-warning"></i> Lỗi: ${data.error}
                        </div>
                    `;
                    removeDuplicatesBtn.disabled = false;
                    removeDuplicatesBtn.innerHTML = '<i class="cil-trash"></i> Loại bỏ trùng lặp';
                }
            })
            .catch(error => {
                console.error('Error removing duplicates:', error);
                document.getElementById('duplicatesContent').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="cil-warning"></i> Lỗi khi loại bỏ trùng lặp: ${error.message}
                    </div>
                `;
                removeDuplicatesBtn.disabled = false;
                removeDuplicatesBtn.innerHTML = '<i class="cil-trash"></i> Loại bỏ trùng lặp';
            });
    }

    // Hàm thêm unique constraint để ngăn trùng lặp
    function addUniqueConstraint() {
        if (!confirm('Bạn có muốn thêm unique constraint để ngăn tạo tài khoản trùng tên trong tương lai?')) {
            return;
        }

        fetch('/api/accounts/add-unique-constraint', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    showToast(data.message, 'success');
                } else {
                    showToast('Lỗi: ' + data.error, 'error');
                }
            })
            .catch(error => {
                console.error('Error adding unique constraint:', error);
                showToast('Lỗi khi thêm unique constraint: ' + error.message, 'error');
            });
    }

    // Hiển thị modal export
    function showExportModal() {
        document.getElementById('exportCount').textContent = selectedAccounts.size;
        new coreui.Modal(document.getElementById('exportModal')).show();
    }

    // Thực hiện thay đổi team hàng loạt
    function executeBulkChangeTeam() {
        const teamId = document.getElementById('bulkTeamSelect').value;
        if (!teamId) {
            showToast('Vui lòng chọn team', 'warning');
            return;
        }

        const accountIds = Array.from(selectedAccounts);

        fetch('/api/bulk_change_team', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                account_ids: accountIds,
                team_id: teamId
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                coreui.Modal.getInstance(document.getElementById('bulkChangeTeamModal')).hide();
                clearSelection();
                if (isAdvancedFilterActive) {
                    filterAccountsAdvanced(1);
                } else {
                    filterAccounts(1);
                }
                loadAccountStats();
            } else {
                showToast(data.error || 'Có lỗi xảy ra', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Có lỗi xảy ra: ' + error.message, 'danger');
        });
    }

    // Thực hiện thay đổi trạng thái hàng loạt
    function executeBulkChangeStatus() {
        const status = document.getElementById('bulkStatusSelect').value;
        if (!status) {
            showToast('Vui lòng chọn trạng thái', 'warning');
            return;
        }

        const accountIds = Array.from(selectedAccounts);

        fetch('/api/bulk_change_status', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                account_ids: accountIds,
                status: status
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                coreui.Modal.getInstance(document.getElementById('bulkChangeStatusModal')).hide();
                clearSelection();
                if (isAdvancedFilterActive) {
                    filterAccountsAdvanced(1);
                } else {
                    filterAccounts(1);
                }
                loadAccountStats();
            } else {
                showToast(data.error || 'Có lỗi xảy ra', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Có lỗi xảy ra: ' + error.message, 'danger');
        });
    }

    // Thực hiện thay đổi nơi Login hàng loạt
    function executeBulkChangeLoggedIn() {
        const loggedIn = document.getElementById('bulkLoggedInSelect').value;
        if (!loggedIn) {
            showToast('Vui lòng chọn nơi Login', 'warning');
            return;
        }

        const accountIds = Array.from(selectedAccounts);

        fetch('/api/bulk_change_logged_in', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: JSON.stringify({
                account_ids: accountIds,
                logged_in: loggedIn
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(data.message, 'success');
                coreui.Modal.getInstance(document.getElementById('bulkChangeLoggedInModal')).hide();
                clearSelection();
                if (isAdvancedFilterActive) {
                    filterAccountsAdvanced(1);
                } else {
                    filterAccounts(1);
                }
                loadAccountStats();
            } else {
                showToast(data.error || 'Có lỗi xảy ra', 'danger');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            showToast('Có lỗi xảy ra: ' + error.message, 'danger');
        });
    }

    // Toggle chọn tất cả cột export
    function toggleAllColumns() {
        const selectAll = document.getElementById('selectAllColumns');
        const checkboxes = document.querySelectorAll('#exportModal input[type="checkbox"]:not(#selectAllColumns)');

        checkboxes.forEach(cb => {
            cb.checked = selectAll.checked;
        });
    }

    // Thực hiện export
    function executeExport() {
        const selectedColumns = [];
        document.querySelectorAll('#exportModal input[type="checkbox"]:checked:not(#selectAllColumns)').forEach(cb => {
            selectedColumns.push(cb.value);
        });

        if (selectedColumns.length === 0) {
            showToast('Vui lòng chọn ít nhất một cột để export', 'warning');
            return;
        }

        const accountIds = Array.from(selectedAccounts);

        // Tạo form để download file
        const form = document.createElement('form');
        form.method = 'POST';
        form.action = '/api/export_accounts';
        form.style.display = 'none';

        // Thêm account IDs
        const accountIdsInput = document.createElement('input');
        accountIdsInput.name = 'account_ids';
        accountIdsInput.value = JSON.stringify(accountIds);
        form.appendChild(accountIdsInput);

        // Thêm columns
        const columnsInput = document.createElement('input');
        columnsInput.name = 'columns';
        columnsInput.value = JSON.stringify(selectedColumns);
        form.appendChild(columnsInput);

        document.body.appendChild(form);
        form.submit();
        document.body.removeChild(form);

        coreui.Modal.getInstance(document.getElementById('exportModal')).hide();
        showToast('Đang tạo file export...', 'info');
    }

    // Override filterAccounts cho admin
    const originalFilterAccounts = window.filterAccounts || filterAccounts;
    window.filterAccounts = function(page) {
        if (typeof isAdvancedFilterActive !== 'undefined' && isAdvancedFilterActive) {
            filterAccountsAdvanced(page);
        } else {
            // Sử dụng renderAccountsTable thay vì logic cũ
            showLoading();
            const searchName = document.getElementById('search_name')?.value || '';
            const teamFilterElement = document.getElementById('team_filter');
            const teamFilter = teamFilterElement ? teamFilterElement.value : '';
            let statusFilter = '';

            if (typeof currentTab !== 'undefined' && currentTab !== 'all') {
                statusFilter = currentTab;
            }

            const url = new URL(window.location.origin + '/accounts');
            url.searchParams.append('page', page);
            url.searchParams.append('search_name', searchName);
            url.searchParams.append('status', statusFilter);
            url.searchParams.append('team_id', teamFilter);
            url.searchParams.append('sort', sortColumn);
            url.searchParams.append('direction', sortDirection);

            fetch(url.toString(), {
                headers: { 'X-Requested-With': 'XMLHttpRequest' }
            })
            .then(response => response.json())
            .then(data => {
                renderAccountsTable(data);
                updatePagination(data.page || 1, data.total_pages || 1);
                updateSortIndicators();

                const statusText = statusFilter ? `Trạng thái: ${statusFilter}` : 'Tất cả trạng thái';
                const statusElement = document.getElementById('current-status');
                if (statusElement) {
                    statusElement.textContent = statusText;
                }

                // Cập nhật số lượng account
                const accountCountElement = document.getElementById('account-count');
                if (data.total_accounts !== undefined) {
                    const currentPage = data.page || 1;
                    const accountsOnPage = data.accounts ? data.accounts.length : 0;
                    const startIndex = ((currentPage - 1) * 20) + 1;
                    const endIndex = startIndex + accountsOnPage - 1;

                    if (accountsOnPage > 0) {
                        accountCountElement.textContent = `Hiển thị ${startIndex}-${endIndex} trong tổng số ${data.total_accounts} tài khoản`;
                    } else {
                        accountCountElement.textContent = `Không có tài khoản nào`;
                    }
                }

                hideLoading();
            })
            .catch(error => {
                console.error("Lỗi khi lọc tài khoản:", error);
                showToast("Lỗi khi lọc tài khoản: " + error.message, "danger");
                hideLoading();
            });
        }
    };

    {% endif %}

    // Hàm global cho tất cả user roles
    function showLoading() {
        // Hiển thị loading spinner
        const loadingHtml = `
            <tr>
                <td colspan="10" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <div class="mt-2">Đang tải dữ liệu...</div>
                </td>
            </tr>`;
        document.getElementById('accounts_body').innerHTML = loadingHtml;
    }

    function hideLoading() {
        // Loading sẽ được thay thế bởi dữ liệu thực
    }

    // Function to toggle revenue enabled status
    function toggleRevenue(accountId, enabled) {
        // Check current enabled count and user's limit
        if (enabled) {
            checkRevenueLimit(accountId, enabled);
        } else {
            updateRevenueStatus(accountId, enabled);
        }
    }

    // Check if user can enable more accounts for revenue
    function checkRevenueLimit(accountId, enabled) {
        fetch('/api/user/revenue-limit')
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    const currentCount = data.current_enabled_count;
                    const limit = data.account_limit;

                    if (enabled && currentCount >= limit) {
                        // Revert the toggle
                        const toggle = document.getElementById(`revenue_${accountId}`);
                        toggle.checked = false;

                        showToast(`Đã đạt giới hạn! Bạn chỉ được phép bật tối đa ${limit} tài khoản để xem doanh thu.`, 'warning');
                        return;
                    }

                    updateRevenueStatus(accountId, enabled);
                } else {
                    showToast('Lỗi khi kiểm tra giới hạn: ' + data.error, 'danger');
                }
            })
            .catch(error => {
                console.error('Error checking revenue limit:', error);
                showToast('Lỗi khi kiểm tra giới hạn', 'danger');
            });
    }

    // Update revenue status in database
    function updateRevenueStatus(accountId, enabled) {
        fetch('/api/accounts/toggle-revenue', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                account_id: accountId,
                revenue_enabled: enabled
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                showToast(`${enabled ? 'Bật' : 'Tắt'} check revenue cho tài khoản thành công`, 'success');
            } else {
                // Revert the toggle on error
                const toggle = document.getElementById(`revenue_${accountId}`);
                toggle.checked = !enabled;
                showToast('Lỗi: ' + data.error, 'danger');
            }
        })
        .catch(error => {
            console.error('Error updating revenue status:', error);
            // Revert the toggle on error
            const toggle = document.getElementById(`revenue_${accountId}`);
            toggle.checked = !enabled;
            showToast('Lỗi khi cập nhật trạng thái', 'danger');
        });
    }

    function showToast(message, type = 'info') {
        // Tạo toast notification
        const toastContainer = document.getElementById('toast-container') || createToastContainer();
        const toastId = 'toast-' + Date.now();

        const toastHtml = `
            <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'danger' ? 'danger' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : 'info'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
                <div class="d-flex">
                    <div class="toast-body">
                        ${message}
                    </div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-coreui-dismiss="toast" aria-label="Close"></button>
                </div>
            </div>`;

        toastContainer.insertAdjacentHTML('beforeend', toastHtml);

        const toastElement = document.getElementById(toastId);
        const toast = new coreui.Toast(toastElement);
        toast.show();

        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toastElement) {
                toastElement.remove();
            }
        }, 5000);
    }

    function createToastContainer() {
        const container = document.createElement('div');
        container.id = 'toast-container';
        container.className = 'toast-container position-fixed top-0 end-0 p-3';
        container.style.zIndex = '1055';
        document.body.appendChild(container);
        return container;
    }

    // Đảm bảo các hàm cần thiết tồn tại
    if (typeof window.getTeamName === 'undefined') {
        window.getTeamName = function(teamId) {
            if (!teamId) return '';
            const team = window.teams.find(t => t[0] == teamId);
            return team ? team[1] : '';
        };
    }

    if (typeof window.getUserName === 'undefined') {
        window.getUserName = function(userId) {
            if (!userId) return '';
            const user = window.users.find(u => u[0] == userId);
            return user ? user[1] : '';
        };
    }
</script>

<!-- Select2 JavaScript -->
<script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
{% endblock %}

{% endblock content %}