#!/usr/bin/env python3
"""
Script to create teams for existing users who don't have teams yet
"""

import sys
import psycopg2

# Database configuration
PG_CONFIG = {
    'host': 'localhost',
    'database': 'sapmmo',
    'user': 'alandoan',
    'password': '',
    'port': 5432
}

def create_teams_for_existing_users():
    """Create teams for users who don't have teams"""
    try:
        print("🔧 Creating teams for existing users...")
        
        # Connect to database
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        # Find users without teams
        cursor.execute('''
            SELECT user_id, username FROM "Users"
            WHERE team_id IS NULL AND is_deleted = 0
            ORDER BY user_id
        ''')
        
        users_without_teams = cursor.fetchall()
        print(f"📊 Found {len(users_without_teams)} users without teams")
        
        if not users_without_teams:
            print("✅ All users already have teams!")
            cursor.close()
            conn.close()
            return True
        
        teams_created = 0
        users_updated = 0
        
        for user_id, username in users_without_teams:
            team_name = username.capitalize()
            print(f"🔧 Creating team for user {username}: {team_name}")
            
            # Check if team name already exists
            cursor.execute('SELECT team_id FROM "Teams" WHERE team_name = %s', (team_name,))
            existing_team = cursor.fetchone()
            
            if existing_team:
                team_id = existing_team[0]
                print(f"   ✅ Using existing team: {team_name} (ID: {team_id})")
            else:
                # Create new team
                cursor.execute('''
                    INSERT INTO "Teams" (team_name, leader_id)
                    VALUES (%s, %s)
                    RETURNING team_id
                ''', (team_name, user_id))
                
                team_id = cursor.fetchone()[0]
                teams_created += 1
                print(f"   ✅ Created team: {team_name} (ID: {team_id})")
            
            # Update user with team_id
            cursor.execute('''
                UPDATE "Users" 
                SET team_id = %s
                WHERE user_id = %s
            ''', (team_id, user_id))
            
            users_updated += 1
            print(f"   ✅ Updated user {username} with team_id: {team_id}")
        
        # Commit changes
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Team creation completed successfully!")
        print(f"📝 Summary:")
        print(f"   - Teams created: {teams_created}")
        print(f"   - Users updated: {users_updated}")
        
        return True
        
    except Exception as e:
        print(f"❌ Team creation failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = create_teams_for_existing_users()
    sys.exit(0 if success else 1)
