#!/usr/bin/env python3
"""
Script to fix sold accounts data - update assigned_user_id and team_id for accounts 
that were sold but missing these fields.

This script:
1. Finds accounts with sold_to_user_id but missing assigned_user_id or team_id
2. Creates teams for users who don't have teams yet
3. Updates accounts with proper assigned_user_id and team_id
"""

import sys
import psycopg2
from datetime import datetime

# Database configuration
PG_CONFIG = {
    'host': 'localhost',
    'database': 'sapmmo',
    'user': 'alandoan',
    'password': '',
    'port': 5432
}

def get_vn_time():
    """Get current Vietnam time"""
    from datetime import datetime, timezone, timedelta
    vn_tz = timezone(timedelta(hours=7))
    return datetime.now(vn_tz)

def fix_sold_accounts_data():
    """Fix sold accounts data"""
    try:
        print("🔧 Starting sold accounts data fix...")
        
        # Connect to database
        conn = psycopg2.connect(**PG_CONFIG)
        cursor = conn.cursor()
        
        # Step 1: Find accounts that need fixing
        print("\n📋 Step 1: Finding accounts that need fixing...")
        
        cursor.execute('''
            SELECT account_id, account_name, sold_to_user_id, assigned_user_id, team_id
            FROM "Accounts"
            WHERE sold_to_user_id IS NOT NULL 
            AND (assigned_user_id IS NULL OR team_id IS NULL)
            AND is_deleted = 0
            ORDER BY account_id
        ''')
        
        accounts_to_fix = cursor.fetchall()
        print(f"📊 Found {len(accounts_to_fix)} accounts that need fixing")
        
        if not accounts_to_fix:
            print("✅ No accounts need fixing!")
            cursor.close()
            conn.close()
            return True
        
        # Step 2: Check users and create teams if needed
        print("\n👥 Step 2: Checking users and creating teams if needed...")
        
        users_processed = set()
        teams_created = 0
        
        for account_id, account_name, sold_to_user_id, assigned_user_id, team_id in accounts_to_fix:
            if sold_to_user_id not in users_processed:
                users_processed.add(sold_to_user_id)
                
                # Get user info
                cursor.execute('''
                    SELECT username, team_id FROM "Users" 
                    WHERE user_id = %s AND is_deleted = 0
                ''', (sold_to_user_id,))
                
                user_result = cursor.fetchone()
                if not user_result:
                    print(f"⚠️  User {sold_to_user_id} not found, skipping...")
                    continue
                
                username, user_team_id = user_result
                
                # If user doesn't have team, create one
                if not user_team_id:
                    team_name = username.capitalize()
                    print(f"🔧 Creating team for user {username}: {team_name}")
                    
                    # Check if team name already exists
                    cursor.execute('SELECT team_id FROM "Teams" WHERE team_name = %s', (team_name,))
                    existing_team = cursor.fetchone()
                    
                    if existing_team:
                        user_team_id = existing_team[0]
                        print(f"   ✅ Using existing team: {team_name} (ID: {user_team_id})")
                    else:
                        cursor.execute('''
                            INSERT INTO "Teams" (team_name, leader_id)
                            VALUES (%s, %s)
                            RETURNING team_id
                        ''', (team_name, sold_to_user_id))
                        
                        user_team_id = cursor.fetchone()[0]
                        teams_created += 1
                        print(f"   ✅ Created team: {team_name} (ID: {user_team_id})")
                    
                    # Update user with team_id
                    cursor.execute('''
                        UPDATE "Users" 
                        SET team_id = %s
                        WHERE user_id = %s
                    ''', (user_team_id, sold_to_user_id))
                    print(f"   ✅ Updated user {username} with team_id: {user_team_id}")
        
        print(f"📊 Teams created: {teams_created}")
        
        # Step 3: Update accounts with proper assigned_user_id and team_id
        print("\n🔄 Step 3: Updating accounts...")
        
        accounts_updated = 0
        
        for account_id, account_name, sold_to_user_id, assigned_user_id, team_id in accounts_to_fix:
            # Get user's current team_id
            cursor.execute('''
                SELECT team_id FROM "Users" 
                WHERE user_id = %s AND is_deleted = 0
            ''', (sold_to_user_id,))
            
            user_result = cursor.fetchone()
            if not user_result:
                print(f"⚠️  User {sold_to_user_id} not found for account {account_name}, skipping...")
                continue
            
            user_team_id = user_result[0]
            
            # Update account
            cursor.execute('''
                UPDATE "Accounts"
                SET assigned_user_id = %s, team_id = %s
                WHERE account_id = %s
            ''', (sold_to_user_id, user_team_id, account_id))
            
            accounts_updated += 1
            print(f"✅ Updated account: {account_name} -> user: {sold_to_user_id}, team: {user_team_id}")
        
        print(f"📊 Accounts updated: {accounts_updated}")
        
        # Step 4: Verify results
        print("\n🔍 Step 4: Verifying results...")
        
        cursor.execute('''
            SELECT COUNT(*) FROM "Accounts"
            WHERE sold_to_user_id IS NOT NULL 
            AND (assigned_user_id IS NULL OR team_id IS NULL)
            AND is_deleted = 0
        ''')
        
        remaining_issues = cursor.fetchone()[0]
        print(f"📊 Remaining accounts with issues: {remaining_issues}")
        
        # Commit changes
        conn.commit()
        cursor.close()
        conn.close()
        
        print(f"\n🎉 Migration completed successfully!")
        print(f"📝 Summary:")
        print(f"   - Teams created: {teams_created}")
        print(f"   - Accounts updated: {accounts_updated}")
        print(f"   - Remaining issues: {remaining_issues}")
        
        return True
        
    except Exception as e:
        print(f"❌ Migration failed: {e}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")
        return False

if __name__ == "__main__":
    success = fix_sold_accounts_data()
    sys.exit(0 if success else 1)
